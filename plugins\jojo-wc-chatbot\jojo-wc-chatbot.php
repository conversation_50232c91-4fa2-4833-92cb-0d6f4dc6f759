<?php
/**
 * Plugin Name: <PERSON><PERSON><PERSON> (All-in-One)
 * Description: Chatbot per WooCommerce: stato ordine + consigli prodotti, con LLM (function calling) e widget chat pronto.
 * Version: 0.2.2
 * Author: <PERSON> + <PERSON> wingman
 * Requires at least: 5.9
 * Requires PHP: 7.4
 */

if (!defined('ABSPATH')) exit;

class JoJo_WC_Chatbot {
  const OPT_GROUP = 'jojo_wc_chatbot';
  const OPT_API_KEY = 'jojo_wc_chatbot_api_key';
  const OPT_PUBLIC_TOKEN = 'jojo_wc_chatbot_public_token';
  const OPT_MODEL = 'jojo_wc_chatbot_model';

  public function __construct() {
    add_action('init', [$this, 'register_shortcode']);
    add_action('wp_enqueue_scripts', [$this, 'enqueue_assets']);
    add_action('rest_api_init', [$this, 'register_routes']);
    add_action('admin_menu', [$this, 'register_settings_page']);
    add_action('admin_init', [$this, 'register_settings']);
  }

  /** ---------- Shortcode & Assets ---------- */
  public function register_shortcode() {
    add_shortcode('jojo_wc_chat', [$this, 'render_chat_shortcode']);
  }

  public function render_chat_shortcode($atts) {
    $atts = shortcode_atts([
      'title' => 'Hai bisogno di aiuto?',
      'subtitle' => 'Chiedimi lo stato del tuo ordine o consigli sui prodotti.',
      'placeholder' => 'Scrivi qui...'
    ], $atts);

    ob_start(); ?>
    <div id="jojo-wc-chatbot" class="jojo-wc-chatbot"
         data-title="<?php echo esc_attr($atts['title']); ?>"
         data-subtitle="<?php echo esc_attr($atts['subtitle']); ?>"
         data-placeholder="<?php echo esc_attr($atts['placeholder']); ?>"></div>
    <?php return ob_get_clean();
  }

  public function enqueue_assets() {
    // CSS/JS inline minimal per il widget; in produzione potresti separare in file nella cartella assets
    $css = "
    .jojo-wc-chatbot{position:fixed;bottom:20px;right:20px;font-family:system-ui,-apple-system,Segoe UI,Roboto,Arial,sans-serif;z-index:9999}
    .jojo-bubble-btn{border:none;border-radius:999px;padding:12px 16px;box-shadow:0 6px 20px rgba(0,0,0,.15);cursor:pointer;background:#111827;color:#fff}
    .jojo-panel{width:320px;max-height:60vh;background:#fff;color:#111;border-radius:16px;box-shadow:0 8px 28px rgba(0,0,0,.2);overflow:hidden;display:none;flex-direction:column}
    .jojo-header{padding:12px 14px;background:#111827;color:#fff}
    .jojo-title{font-weight:700;font-size:14px}
    .jojo-subtitle{font-size:12px;opacity:.9}
    .jojo-messages{padding:12px;overflow:auto;background:#f7f7f7;height:260px}
    .jojo-msg{display:inline-block;background:#fff;padding:8px 10px;border-radius:10px;margin:6px 0;max-width:85%;box-shadow:0 1px 2px rgba(0,0,0,.06)}
    .jojo-msg.user{background:#dbeafe;margin-left:auto;display:block}
    .jojo-input{display:flex;gap:8px;padding:10px;border-top:1px solid #e5e7eb;background:#fff}
    .jojo-input input{flex:1;padding:10px 12px;border:1px solid #d1d5db;border-radius:8px;outline:none}
    .jojo-input button{border:none;padding:10px 12px;border-radius:8px;background:#111827;color:#fff;cursor:pointer}
    .jojo-actions{display:flex;gap:6px;padding:8px 10px;border-top:1px dashed #e5e7eb;background:#fafafa}
    .jojo-actions button{border:1px solid #e5e7eb;background:#fff;border-radius:999px;padding:6px 10px;font-size:12px;cursor:pointer}
    ";
    wp_register_style('jojo-wc-chatbot-inline', false);
    wp_enqueue_style('jojo-wc-chatbot-inline');
    wp_add_inline_style('jojo-wc-chatbot-inline', $css);

    $data = [
      'restUrl' => esc_url_raw( rest_url('jojo/v1') ),
      'siteTokenRequired' => (bool) get_option(self::OPT_PUBLIC_TOKEN),
      'siteToken' => (string) get_option(self::OPT_PUBLIC_TOKEN),
      'nonce' => wp_create_nonce('wp_rest')
    ];

    $js = "
    (function(){
      function el(t,c,x){const e=document.createElement(t); if(c) e.className=c; if(x) e.textContent=x; return e;}
      function init(container){
        const title = container.dataset.title || 'Hai bisogno di aiuto?';
        const subtitle = container.dataset.subtitle || 'Chiedimi lo stato del tuo ordine o consigli sui prodotti.';
        const placeholder = container.dataset.placeholder || 'Scrivi qui...';

        const bubble = el('button','jojo-bubble-btn','Chat');
        const panel = el('div','jojo-panel');
        const header = el('div','jojo-header');
        header.appendChild(el('div','jojo-title',title));
        header.appendChild(el('div','jojo-subtitle',subtitle));
        const messages = el('div','jojo-messages');
        const greet = el('div','jojo-msg'); greet.innerHTML='Ciao! Posso aiutarti con:<br/>• <b>Stato ordine</b>: scrivi numero ordine + email/cognome<br/>• <b>Consigli prodotti</b>: es. “cuffie bluetooth sotto 50€”';
        messages.appendChild(greet);
        const actions = el('div','jojo-actions');
        const b1 = el('button','', 'Stato ordine'); b1.onclick = ()=>{ input.value='Stato ordine 1234, email <EMAIL>'; input.focus(); };
        const b2 = el('button','', 'Consigli sotto 50€'); b2.onclick = ()=>{ input.value='Cerco cuffie bluetooth sotto 50€'; input.focus(); };
        actions.appendChild(b1); actions.appendChild(b2);
        const inputWrap = el('div','jojo-input');
        const input = el('input',''); input.placeholder = placeholder;
        const send = el('button','', 'Invia');
        inputWrap.appendChild(input); inputWrap.appendChild(send);
        panel.appendChild(header); panel.appendChild(messages); panel.appendChild(actions); panel.appendChild(inputWrap);
        container.appendChild(panel); container.appendChild(bubble);

        bubble.onclick = ()=>{ panel.style.display = (panel.style.display==='flex')?'none':'flex'; panel.style.flexDirection='column'; };

        function pushMessage(text, who){ const m=el('div','jojo-msg'+(who==='user'?' user':'')); m.textContent=text; messages.appendChild(m); messages.scrollTop=messages.scrollHeight; }

        async function askLLM(message){
          pushMessage(message,'user');
          const url = '".esc_js($data['restUrl'])."/chat';
          const headers = {'Content-Type':'application/json','X-WP-Nonce':'".esc_js($data['nonce'])."'};
          const res = await fetch(url,{method:'POST',headers,body:JSON.stringify({message})});
          const json = await res.json();
          pushMessage(json.reply || 'Non ho capito, puoi riformulare?','bot');
        }

        function sendMessage(){ const v=(input.value||'').trim(); if(!v) return; askLLM(v); input.value=''; }
        input.addEventListener('keydown', e=>{ if(e.key==='Enter') sendMessage(); });
        send.addEventListener('click', sendMessage);
      }
      document.addEventListener('DOMContentLoaded', function(){
        var c=document.getElementById('jojo-wc-chatbot'); if(c) init(c);
      });
    })();";
    wp_register_script('jojo-wc-chatbot-inline', false);
    wp_enqueue_script('jojo-wc-chatbot-inline');
    wp_add_inline_script('jojo-wc-chatbot-inline', $js);
  }

  /** ---------- REST API ---------- */
  public function register_routes() {
    register_rest_route('jojo/v1', '/chat', [
      'methods' => 'POST',
      'callback' => [$this, 'route_chat'],
      'permission_callback' => '__return_true',
    ]);

    register_rest_route('jojo/v1', '/llm', [
      'methods' => 'POST',
      'callback' => [$this, 'route_llm'],
      'permission_callback' => [$this, 'check_public_token'],
    ]);

    // Self-test (solo admin)
    register_rest_route('jojo/v1', '/selftest', [
      'methods' => 'GET',
      'callback' => [$this, 'route_selftest'],
      'permission_callback' => function(){ return current_user_can('manage_options'); },
    ]);
  }

  public function check_public_token(\WP_REST_Request $req) {
    $required = get_option(self::OPT_PUBLIC_TOKEN);
    if (!$required) return true;
    $got = $req->get_header('x-jojo-token');
    return hash_equals($required, (string)$got);
  }

  public function route_chat(\WP_REST_Request $req) {
    $body = $req->get_json_params();
    $message = isset($body['message']) ? sanitize_text_field($body['message']) : '';
    if (!$message) {
      return new \WP_REST_Response(['reply' => 'Scrivimi una domanda.'], 200);
    }

    $args = [
      'headers' => [ 'Content-Type' => 'application/json' ],
      'body' => wp_json_encode(['message' => $message], JSON_UNESCAPED_UNICODE),
      'timeout' => 25,
    ];
    $tok = get_option(self::OPT_PUBLIC_TOKEN);
    if (!empty($tok)) $args['headers']['X-JOJO-TOKEN'] = $tok;

    $res = wp_remote_post( rest_url('jojo/v1/llm'), $args );
    if (is_wp_error($res)) {
      return new \WP_REST_Response(['reply' => 'Errore di collegamento LLM.'], 200);
    }
    $json = json_decode(wp_remote_retrieve_body($res), true);
    return new \WP_REST_Response($json, 200); 
    return new \WP_REST_Response([
      'reply' => $json['reply'] ?? 'Non ho capito, puoi riformulare?',
    ], 200);
  }

  public function route_llm(\WP_REST_Request $req) {
    $msg = $req->get_param('message') ?: '';
    $messages = [
      [
        "role" => "system",
        "content" => "Sei un assistente per e-commerce WooCommerce. Compiti: (1) Stato ordine. (2) Consigli prodotto. \
Devi SEMPRE usare i tools definiti, MAI inventare dati. Per stato ordine richiedi almeno email o cognome. \
Se mancano campi, chiedili in modo conciso. Rispondi in italiano, tono cortese e sintetico."
      ],
      [ "role" => "user", "content" => $msg ]
    ];

    // --- PRIMA CHIAMATA: decide se usare un tool e con quali args ---
    $first = $this->openai_chat([
      "messages" => $messages,
      "tools" => [
        [
          "type" => "function",
          "function" => [
            "name" => "get_order_status",
            "description" => "Recupera stato ordine da WooCommerce. Richiede order_id e (email oppure last_name).",
            "parameters" => [
              "type" => "object",
              "properties" => [
                "order_id" => ["type" => "integer"],
                "email" => ["type" => "string"],
                "last_name" => ["type" => "string"]
              ],
              "required" => ["order_id"],
              "additionalProperties" => false
            ]
          ]
        ],
        [
          "type" => "function",
          "function" => [
            "name" => "recommend_products",
            "description" => "Suggerisci prodotti disponibili filtrando per testo/categoria/budget.",
            "parameters" => [
              "type" => "object",
              "properties" => [
                "search" => ["type" => "string"],
                "category" => ["type" => "string"],
                "min_price" => ["type" => "number"],
                "max_price" => ["type" => "number"],
                "limit" => ["type" => "integer", "default" => 5]
              ],
              "additionalProperties" => false
            ]
          ]
        ]
      ],
      "tool_choice" => "auto",
      "temperature" => 0.2
    ]);

    if (!empty($first['error'])) {
      return new \WP_REST_Response(["reply" => $first['error']], 200);
    }

    $choice = $first['choices'][0]['message'] ?? [];
    $tool_calls = $choice['tool_calls'] ?? [];

    if ($tool_calls) {
      $tool_messages = [];
      $recommended_products = [];
      foreach ($tool_calls as $call) {
        $name = $call['function']['name'] ?? '';
        $args = json_decode($call['function']['arguments'] ?? '{}', true) ?: [];
        $result = null;

        switch ($name) {
          case 'get_order_status':
            $result = $this->php_get_order_status($args);
            break;
          case 'recommend_products':
            $result = $this->php_recommend_products($args);
            if (!empty($result['results']) && is_array($result['results'])) {
              $recommended_products = $result['results']; // <--- aggiunta
            }
            break;
          default:
            $result = ["error" => "Tool sconosciuto: ".$name];
        }

        $tool_messages[] = [
          "role" => "tool",
          "tool_call_id" => $call['id'],
          "name" => $name,
          "content" => wp_json_encode($result, JSON_UNESCAPED_UNICODE)
        ];
      }

      // --- SECONDA CHIAMATA: fornisci i risultati tool e fatti scrivere la risposta ---
      $second_messages = array_merge(
        $messages,
        [ $choice ],     // assistant con i tool_calls
        $tool_messages   // risultati dei tool
      );

      $final = $this->openai_chat([
        "messages" => $second_messages,
        "temperature" => 0.2
      ]);

      if (!empty($final['error'])) {
        return new \WP_REST_Response(["reply" => $final['error']], 200);
      }

      $txt = $final['choices'][0]['message']['content'] ?? '';
      return new \WP_REST_Response([
          "reply" => trim($txt ?: "Non ho capito, puoi riformulare?"),
          "products" => $recommended_products // <--- aggiunta
        ], 200);
    }

    // Nessun tool: probabilmente chiede info mancanti/clarification
    $txt = $choice['content'] ?? '';
    return new \WP_REST_Response(["reply" => trim($txt ?: "Puoi riformulare la domanda?")], 200);
  }

  /** ---------- Tool implementations ---------- */
  private function php_get_order_status($args) {
    $order_id = intval($args['order_id'] ?? 0);
    $email = sanitize_email($args['email'] ?? '');
    $last_name = sanitize_text_field($args['last_name'] ?? '');

    if (!$order_id) return ["error" => "order_id mancante."];
    if (!class_exists('WC_Order')) return ["error" => "WooCommerce non attivo."];

    $order = wc_get_order($order_id);
    if (!$order) return ["error" => "Ordine non trovato."];

    $billing_email = $order->get_billing_email();
    $billing_last  = $order->get_billing_last_name();

    $email_ok = $email ? (mb_strtolower($email) === mb_strtolower($billing_email)) : false;
    $last_ok  = $last_name ? (mb_strtolower($last_name) === mb_strtolower($billing_last)) : false;

    if (!$email_ok && !$last_ok) {
      return ["error" => "Verifica fallita: email o cognome non combaciano."];
    }

    $status  = wc_get_order_status_name($order->get_status());
    $total   = wp_strip_all_tags(wc_price($order->get_total()));
    $created = $order->get_date_created() ? $order->get_date_created()->date_i18n(get_option('date_format')) : '';
    $tracking = $this->get_tracking_info($order);

    return [
      "ok" => true,
      "order_id" => $order->get_id(),
      "status" => $status,
      "total" => $total,
      "created" => $created,
      "tracking" => $tracking
    ];
  }

  private function get_tracking_info($order) {
    $meta_keys = ['_tracking_number', '_tracking_provider', '_tracking_url', 'tracking_number', 'tracking_url'];
    $out = [];
    foreach ($meta_keys as $k) {
      $v = $order->get_meta($k);
      if (!empty($v)) $out[$k] = $v;
    }
    $notes = wc_get_order_notes(['order_id' => $order->get_id()]);
    foreach ($notes as $note) {
      if (preg_match('#https?://[^\s]+#', $note->content, $m)) {
        $out['note_link'] = $m[0];
        break;
      }
    }
    return $out;
  }

  private function php_recommend_products($args) {
    $q = [
      'status' => 'publish',
      'stock_status' => 'instock',
      'limit' => max(1, min(12, intval($args['limit'] ?? 5))),
      'orderby' => 'date',
      'order' => 'DESC',
    ];
    if (!empty($args['category'])) $q['category'] = [sanitize_title($args['category'])];
    if (!empty($args['search']))   $q['s'] = sanitize_text_field($args['search']);

    $meta = [];
    if (!empty($args['min_price'])) $meta[] = ['key'=>'_price','value'=>floatval($args['min_price']),'compare'=>'>=','type'=>'NUMERIC'];
    if (!empty($args['max_price'])) $meta[] = ['key'=>'_price','value'=>floatval($args['max_price']),'compare'=>'<=','type'=>'NUMERIC'];
    if ($meta) $q['meta_query'] = $meta;

    $ps = wc_get_products($q);
    $out = [];
    foreach ($ps as $p) {
      $out[] = [
        "id" => $p->get_id(),
        "name" => $p->get_name(),
        "price" => wc_price($p->get_price()),
        "url" => get_permalink($p->get_id()),
        "image" => get_the_post_thumbnail_url($p->get_id(), 'medium') ?: wc_placeholder_img_src(),
        "short_description" => wp_strip_all_tags($p->get_short_description()),
      ];
    }
    return ["ok" => true, "results" => $out];
  }

  /** ---------- OpenAI Chat Completions (stabile) ---------- */
  private function openai_chat($payload) {
    $apiKey = (string) get_option(self::OPT_API_KEY);
    if (!$apiKey && defined('OPENAI_API_KEY')) $apiKey = OPENAI_API_KEY;
    if (!$apiKey) return ["error" => "Manca la API key. Vai su Impostazioni > JoJo Chatbot."];

    if (empty($payload['model'])) {
      $payload['model'] = (string) get_option(self::OPT_MODEL, 'gpt-4o-mini');
    }
    if (!isset($payload['temperature'])) {
      $payload['temperature'] = 0.2;
    }

    $args = [
      'headers' => [
        'Authorization' => 'Bearer '.$apiKey,
        'Content-Type' => 'application/json'
      ],
      'body' => wp_json_encode($payload, JSON_UNESCAPED_UNICODE),
      'timeout' => 25,
    ];
    $res = wp_remote_post('https://api.openai.com/v1/chat/completions', $args);
    if (is_wp_error($res)) return ["error" => "Errore API (connessione): ".$res->get_error_message()];
    $code = wp_remote_retrieve_response_code($res);
    $body = wp_remote_retrieve_body($res);
    if ($code >= 400) {
      return ["error" => "Errore API ($code): ".substr($body,0,350)];
    }
    return json_decode($body, true);
  }

  /** ---------- Self-test ---------- */
  public function route_selftest(\WP_REST_Request $req) {
    $apiKey = (string) get_option(self::OPT_API_KEY);
    if (!$apiKey && defined('OPENAI_API_KEY')) $apiKey = OPENAI_API_KEY;
    if (!$apiKey) return new \WP_REST_Response(['ok'=>false,'error'=>'API key mancante. Imposta la chiave.'],200);

    $args = [
      'headers' => [ 'Authorization' => 'Bearer '.$apiKey ],
      'timeout' => 15,
    ];
    $res = wp_remote_get('https://api.openai.com/v1/models', $args);
    if (is_wp_error($res)) return new \WP_REST_Response(['ok'=>false,'wp_error'=>$res->get_error_message()],200);
    $code = wp_remote_retrieve_response_code($res);
    $body = wp_remote_retrieve_body($res);
    return new \WP_REST_Response(['ok'=> $code<400, 'status'=>$code, 'body_excerpt'=> substr($body,0,200)],200);
  }

  /** ---------- Admin settings ---------- */
  public function register_settings_page() {
    add_options_page('JoJo WC Chatbot', 'JoJo Chatbot', 'manage_options', 'jojo-wc-chatbot', [$this, 'settings_page_html']);
  }

  public function register_settings() {
    register_setting(self::OPT_GROUP, self::OPT_API_KEY, ['type'=>'string','sanitize_callback'=>'sanitize_text_field']);
    register_setting(self::OPT_GROUP, self::OPT_PUBLIC_TOKEN, ['type'=>'string','sanitize_callback'=>'sanitize_text_field']);
    register_setting(self::OPT_GROUP, self::OPT_MODEL, ['type'=>'string','sanitize_callback'=>'sanitize_text_field']);

    add_settings_section('main', 'Impostazioni principali', function(){
      echo '<p>Configura la tua API key e (opzionale) un token pubblico per proteggere l’endpoint.</p>';
    }, 'jojo-wc-chatbot');

    add_settings_field(self::OPT_API_KEY, 'OpenAI API Key', function(){
      $val = esc_attr(get_option(self::OPT_API_KEY, ''));
      echo '<input type="password" name="'.self::OPT_API_KEY.'" value="'.$val.'" class="regular-text" />';
      echo '<p class="description">Consigliato: definisci OPENAI_API_KEY in wp-config.php. In alternativa, inseriscila qui.</p>';
    }, 'jojo-wc-chatbot', 'main');

    add_settings_field(self::OPT_MODEL, 'Modello', function(){
      $val = esc_attr(get_option(self::OPT_MODEL, 'gpt-4o-mini'));
      echo '<input type="text" name="'.self::OPT_MODEL.'" value="'.$val.'" class="regular-text" />';
      echo '<p class="description">Default consigliato: gpt-4o-mini. Compatibili anche varianti recenti con function calling.</p>';
    }, 'jojo-wc-chatbot', 'main');

    add_settings_field(self::OPT_PUBLIC_TOKEN, 'Token pubblico (opzionale)', function(){
      $val = esc_attr(get_option(self::OPT_PUBLIC_TOKEN, ''));
      echo '<input type="text" name="'.self::OPT_PUBLIC_TOKEN.'" value="'.$val.'" class="regular-text" />';
      echo '<p class="description">Se impostato, le chiamate alla rotta /jojo/v1/llm richiedono header X-JOJO-TOKEN uguale a questo valore.</p>';
    }, 'jojo-wc-chatbot', 'main');
  }

  public function settings_page_html() {
    if (!current_user_can('manage_options')) return; ?>
    <div class="wrap">
      <h1>JoJo WC Chatbot</h1>
      <form action="options.php" method="post">
        <?php
          settings_fields(self::OPT_GROUP);
          do_settings_sections('jojo-wc-chatbot');
          submit_button();
        ?>
      </form>
      <hr/>
      <h2>Uso rapido</h2>
      <ol>
        <li>Inserisci la API key e salva.</li>
        <li>Aggiungi lo shortcode <code>[jojo_wc_chat]</code> in una pagina (es. /assistenza o nel footer).</li>
        <li>Prova a chiedere: “Stato dell’ordine 1234” oppure “Cerco cuffie bluetooth sotto 50€”</li>
      </ol>
    </div>
    <?php
  }
}
new JoJo_WC_Chatbot();
