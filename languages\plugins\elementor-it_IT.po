# Translation of Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release) in Italian
# This file is distributed under the same license as the Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-09-04 07:49:29+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: it\n"
"Project-Id-Version: Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release)\n"

#: includes/widgets/heading.php:489
msgid "Accessible structure matters"
msgstr "L'accessibilità delle strutture è importante"

#: includes/widgets/heading.php:464
msgid "Make sure your page is structured with accessibility in mind. Ally helps detect and fix common issues across your site."
msgstr "Assicurati che la tua pagina sia strutturata tenendo conto dei criteri di accessibilità. Ally ti aiuta a individuare e risolvere i problemi più comuni presenti nel tuo sito."

#: core/utils/hints.php:505
msgid "Ally web accessibility"
msgstr "Ally - Web Accessibility"

#: core/utils/hints.php:452
msgid "Customize the widget's look, position and the capabilities available for your visitors."
msgstr "Personalizza l'aspetto, la posizione e le funzionalità del widget disponibili per chi visita il tuo sito."

#: core/utils/hints.php:451
msgid "Connect the Ally plugin to your account to access all of it's accessibility features."
msgstr "Collega il plugin Ally al tuo account per accedere a tutte le sue funzioni di accessibilità."

#: core/utils/hints.php:450
msgid "Activate the Ally plugin to turn its accessibility features on across your site."
msgstr "Attiva il plugin Ally per abilitare le sue funzionalità di accessibilità su tutto il tuo sito."

#: core/utils/hints.php:449
msgid "Install Ally to add an accessibility widget visitors can use to navigate your site."
msgstr "Installa Ally per aggiungere un widget di accessibilità che chi visita il tuo sito possa utilizzare per navigarlo."

#: core/editor/loader/common/editor-common-scripts-settings.php:132
msgid "Ally Accessibility"
msgstr "Ally Accessibility"

#: core/admin/menu/cloud-hosting-plans-menu-item.php:42
#: core/admin/menu/cloud-hosting-plans-menu-item.php:50
msgid "Hosting Plans"
msgstr "Piani di hosting"

#: includes/widgets/heading.php:479
msgid "Connect to Ally"
msgstr "Connetti Ally"

#. translators: %s: Platform name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:270
msgid "Open %s"
msgstr "Apri %s"

#. translators: %s: Accessible name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:112
#: modules/floating-buttons/classes/render/floating-bars-core-render.php:112
msgid "Close %s"
msgstr "Chiudi %s"

#. translators: %s: Accessible name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:87
msgid "Toggle %s"
msgstr "Attiva/disattiva %s"

#. translators: %s: Maximum number of icons allowed.
#: modules/link-in-bio/base/widget-link-in-bio-base.php:572
msgid "Add up to %s icons"
msgstr "Aggiungi fino a %s icone"

#. translators: 1: Minimum items, 2: Items limit.
#: modules/floating-buttons/base/widget-contact-button-base.php:752
msgid "Add between %1$s to %2$s contact buttons"
msgstr "Aggiungi tra %1$s e %2$s pulsanti di contatto"

#: app/modules/import-export-customization/runners/import/site-settings.php:172
#: app/modules/import-export/runners/import/site-settings.php:172
msgid "Theme: %1$s has been successfully installed"
msgstr "Tema: %1$s è stato installato con successo"

#: app/modules/import-export-customization/runners/import/site-settings.php:153
#: app/modules/import-export/runners/import/site-settings.php:153
msgid "Theme: %1$s is already used"
msgstr "Tema: %1$s è già utilizzato"

#: app/modules/import-export-customization/runners/import/site-settings.php:168
#: app/modules/import-export/runners/import/site-settings.php:168
msgid "Failed to install theme: %1$s"
msgstr "Impossibile installare il tema: %1$s"

#. translators: %s: Maximum number of CTA links allowed.
#: modules/link-in-bio/base/widget-link-in-bio-base.php:316
msgid "Add up to %s CTA links"
msgstr "Aggiungi fino a un massimo di %s link CTA"

#. translators: %s: Maximum number of images allowed.
#: modules/link-in-bio/base/widget-link-in-bio-base.php:228
msgid "Add up to %s Images"
msgstr "Aggiungi fino a un massimo di %s immagini"

#. translators: %s: Items limit.
#: modules/floating-buttons/base/widget-contact-button-base.php:766
msgid "Add up to %s contact buttons"
msgstr "Aggiungi fino a un massimo di %s pulsanti di contatto"

#: includes/widgets/alert.php:240
msgid "Side Border Width"
msgstr "Larghezza del bordo laterale"

#: includes/widgets/alert.php:229
msgid "Side Border Color"
msgstr "Colore del bordo laterale"

#: modules/variables/classes/rest-api.php:371
msgid "Variable label already exists"
msgstr "L'etichetta della variabile esiste già"

#: app/modules/import-export-customization/runners/import/site-settings.php:161
#: app/modules/import-export/runners/import/site-settings.php:161
msgid "Theme: %1$s has already been installed and activated"
msgstr "Tema: %1$s è già stato installato e attivato"

#: app/modules/import-export-customization/module.php:157
msgid "Open the Library"
msgstr "Apri la libreria"

#: app/modules/import-export-customization/module.php:148
msgid "Upload .zip file"
msgstr "Carica il file .zip"

#: app/modules/import-export-customization/module.php:145
msgid "Apply a Website Template"
msgstr "Applica un template website"

#: app/app.php:289
msgid "Enable advanced customization options for import/export functionality."
msgstr "Abilita le opzioni di personalizzazione avanzate per le funzionalità di importazione/esportazione."

#: app/app.php:288
msgid "Import/Export Customization"
msgstr "Personalizza la funzione di importazione/esportazione"

#: core/admin/admin-notices.php:494
msgid "Collecting leads is just the beginning. With Send by Elementor, you can manage contacts, launch automations, and turn form submissions into sales."
msgstr "La raccolta di contatti è solo l'inizio. Con Send di Elementor, puoi gestire i contatti, lanciare automazioni e trasformare gli invii di moduli in vendite."

#: core/admin/admin-notices.php:490
msgid "Turn leads into loyal shoppers"
msgstr "Trasforma i contatti in clientela fedele"

#: modules/variables/classes/rest-api.php:386
msgid "Unexpected server error"
msgstr "C'è stato un errore del server inaspettato"

#: modules/variables/classes/rest-api.php:379
msgid "Variable not found"
msgstr "La variabile non è stata trovata"

#: modules/variables/classes/rest-api.php:363
msgid "Reached the maximum number of variables"
msgstr "È stato raggiunto il numero massimo di variabili"

#: modules/variables/classes/rest-api.php:214
msgid "Value cannot exceed %d characters"
msgstr "Il valore non può superare i %d caratteri"

#: modules/variables/classes/rest-api.php:208
msgid "Value cannot be empty"
msgstr "Il valore non può essere vuoto"

#: modules/variables/classes/rest-api.php:194
msgid "Label cannot exceed %d characters"
msgstr "L'etichetta non può eccedere i %d caratteri"

#: modules/variables/classes/rest-api.php:188
msgid "Label cannot be empty"
msgstr "L'etichetta non può essere vuota"

#: modules/variables/classes/rest-api.php:167
msgid "ID cannot exceed %d characters"
msgstr "L'ID non può superare i %d caratteri"

#: modules/variables/classes/rest-api.php:161
msgid "ID cannot be empty"
msgstr "L'ID non può essere vuoto"

#: modules/global-classes/module.php:64
msgid "Enforce global classes capabilities."
msgstr "Applica le capacità delle classi globali."

#: modules/global-classes/module.php:63
msgid "Enforce global classes capabilities"
msgstr "Applica le capacità delle classi globali"

#: modules/cloud-kit-library/module.php:144
msgid "Cloud-Kits is not instantiated."
msgstr "Cloud-Kits non è instanziato."

#: modules/cloud-kit-library/connect/cloud-kits.php:125
msgid "Failed to create kit: Content upload failed"
msgstr "La creazione del kit non è andata a buon fine: il caricamento del contenuto non è riuscito"

#: modules/cloud-kit-library/connect/cloud-kits.php:117
msgid "Failed to create kit: No upload URL provided"
msgstr "La creazione del kit non è andata a buon fine: non è stato fornito nessun URL da caricare"

#: modules/cloud-kit-library/connect/cloud-kits.php:111
msgid "Failed to create kit: Invalid response"
msgstr "La creazione del kit non è andata a buon fine: risposta non valida"

#: modules/cloud-kit-library/connect/cloud-kits.php:17
msgid "Cloud Kits"
msgstr "Cloud Kits"

#: modules/atomic-widgets/module.php:168
msgid "Enforce atomic widgets capabilities."
msgstr "Applica le capacità dei widget atomici"

#: modules/atomic-widgets/module.php:167
msgid "Enforce atomic widgets capabilities"
msgstr "Applica le capacità dei widget atomici"

#: modules/atomic-widgets/module.php:160
msgid "Enable V4 Indication Popovers"
msgstr "Abilita V4 Indication Popovers"

#: modules/atomic-widgets/module.php:159
msgid "V4 Indications Popover"
msgstr "V4 Indications Popover"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:87
msgid "Related videos"
msgstr "Video correlati"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:84
msgid "Player controls"
msgstr "Controlli del player"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:83
msgid "Lazy load"
msgstr "Lazy load"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:79
msgid "End time"
msgstr "Orario di fine"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:78
msgid "Start time"
msgstr "Orario di inizio"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:75
msgid "YouTube URL"
msgstr "URL di YouTube"

#: modules/atomic-widgets/elements/has-atomic-base.php:134
msgid "ID"
msgstr "ID"

#: includes/widgets/common-base.php:222
msgid "Custom Mask"
msgstr "Maschera personalizzata"

#: includes/widgets/common-base.php:208
msgid "Hexagon Donut"
msgstr "Forma a ciambella esagonale"

#: includes/widgets/common-base.php:196
msgid "Trapezoid Down"
msgstr "Forma trapezoidale verso il basso"

#: includes/widgets/common-base.php:192
msgid "Trapezoid Up"
msgstr "Forma trapezoidale verso l'alto"

#: includes/widgets/common-base.php:188
msgid "Parallelogram left"
msgstr "Forma a parallelogramma a sinistra"

#: includes/widgets/common-base.php:184
msgid "Parallelogram right"
msgstr "Forma a parallelogramma a destra"

#: includes/widgets/common-base.php:180
msgid "Octagon"
msgstr "Forma ottagonale"

#: includes/widgets/common-base.php:176
msgid "Heptagon"
msgstr "Forma settagonale"

#: includes/widgets/common-base.php:172
msgid "Hexagon horizontal"
msgstr "Forma esagonale orizzontale"

#: includes/widgets/common-base.php:168
msgid "Hexagon vertical"
msgstr "Forma esagonale verticale"

#: includes/widgets/common-base.php:164
msgid "Pentagon"
msgstr "Forma pentagonale"

#: includes/widgets/common-base.php:160
msgid "Diamond"
msgstr "Forma a diamante"

#: includes/widgets/common-base.php:152
msgid "Pill horizontal"
msgstr "Forma a pillola orizzontale"

#: includes/widgets/common-base.php:148
msgid "Pill vertical"
msgstr "Forma a pillola verticale"

#: includes/widgets/common-base.php:144
msgid "Oval horizontal"
msgstr "Forma ovale orizzontale"

#: includes/widgets/common-base.php:140
msgid "Oval vertical"
msgstr "Forma ovale verticale"

#: includes/settings/settings-page.php:396
msgid "Data Sharing"
msgstr "Condividi i dati"

#: core/settings/editor-preferences/model.php:166
msgid "These will guide you through the first steps of creating your site."
msgstr "Questi ti guideranno attraverso le prime fasi di creazione di un sito."

#: core/settings/editor-preferences/model.php:161
msgid "Show launchpad checklist"
msgstr "Mostra la lista di controllo launchpad"

#: core/common/modules/connect/rest/rest-api.php:163
msgid "Elementor Library app is not available."
msgstr "L'app della Libreria di Elementor non è disponibile."

#: core/common/modules/connect/rest/rest-api.php:92
msgid "Failed to connect to Elementor Library."
msgstr "La connessione alla Libreria di Elementor non è andata a buon fine."

#: core/admin/admin-notices.php:279
msgid "Opt in"
msgstr "Opt in"

#: core/admin/admin-notices.php:276
msgid "Update regarding usage data collection"
msgstr "Aggiornamento in merito alla raccolta dei dati di utilizzo"

#: core/admin/admin-notices.php:272
msgid "We're updating our Terms and Conditions to include the collection of usage and behavioral data. This information helps us understand how you use Elementor so we can make informed improvements to the product."
msgstr "Stiamo aggiornando i nostri Termini e condizioni per includere la raccolta di dati di utilizzo e comportamentali. Queste informazioni ci aiutano a capire come utilizzi Elementor, in modo da poter apportare miglioramenti consapevoli al prodotto."

#: core/admin/admin-notices.php:234
msgid "Want to shape the future of web creation?"
msgstr "Vuoi dare forma al futuro della creazione del web?"

#: core/admin/admin-notices.php:218 includes/settings/settings-page.php:403
msgid "Become a super contributor by helping us understand how you use our service to enhance your experience and improve our product."
msgstr "Diventa un super collaboratore aiutandoci a capire come utilizzi il nostro servizio per migliorare la tua esperienza e il nostro prodotto."

#: app/modules/import-export/module.php:159
msgid "Import from library"
msgstr "Importa dalla libreria"

#: app/modules/import-export-customization/module.php:150
#: app/modules/import-export/module.php:152
msgid "You can import design and settings from a .zip file or choose from the library."
msgstr "Puoi importare il design e le impostazioni da un file .zip o sceglierli dalla libreria."

#: app/modules/import-export-customization/module.php:142
#: app/modules/import-export/module.php:144
msgid "You can download this website as a .zip file, or upload it to the library."
msgstr "Puoi scaricare questo website come un file .zip, o caricarlo nella libreria."

#: app/modules/import-export-customization/module.php:137
#: app/modules/import-export/module.php:139
msgid "Export this website"
msgstr "Esporta questo website"

#: app/modules/import-export-customization/module.php:226
#: app/modules/import-export/module.php:228
msgid "Remove Website Template"
msgstr "Rimuovi il template website"

#: app/modules/import-export-customization/module.php:219
#: app/modules/import-export/module.php:221
msgid "Remove the most recent Website Template"
msgstr "Rimuovi il più recente template website"

#: app/modules/import-export/module.php:147
msgid "Import website templates"
msgstr "Importa i template website"

#: app/modules/import-export-customization/module.php:196
#: app/modules/import-export/module.php:198
msgid "Here’s where you can export this website as a .zip file, upload it to the cloud, or start the process of applying an existing template to your site."
msgstr "Qui è dove puoi esportare questo sito in un formato file .zip, caricarlo nel cloud, o iniziare il processo di applicazione di un template esistente al tuo sito."

#: app/modules/import-export-customization/module.php:116
#: app/modules/import-export-customization/module.php:119
#: app/modules/import-export/module.php:120
#: app/modules/import-export/module.php:123
#: app/modules/kit-library/kit-library-menu-item.php:22
#: app/modules/kit-library/module.php:35 app/modules/kit-library/module.php:36
#: core/common/modules/finder/categories/general.php:78
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:1708
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3900
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4646
msgid "Website Templates"
msgstr "Template website"

#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:75
msgid "Image resolution"
msgstr "Risoluzione immagine"

#: modules/variables/module.php:25
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:11
#: assets/js/packages/editor-variables/editor-variables.strings.js:12
#: assets/js/packages/editor-variables/editor-variables.strings.js:13
#: assets/js/packages/editor-variables/editor-variables.strings.js:25
msgid "Variables"
msgstr "Variabili"

#: modules/cloud-library/module.php:142 assets/js/editor.js:9815
msgid "Connect to your Elementor account"
msgstr "Connettiti al tuo account Elementor"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:53
msgid "This is a title"
msgstr "Questo è un titolo"

#: includes/editor-templates/templates.php:493
msgid "Give your template a name"
msgstr "Assegna al tuo template un nome"

#: includes/editor-templates/templates.php:147
msgid "List view"
msgstr "Vista elenco"

#: includes/editor-templates/templates.php:143
msgid "Grid view"
msgstr "Vista griglia"

#: includes/editor-templates/templates.php:348
#: modules/cloud-library/module.php:77
msgid "Folder"
msgstr "Cartella"

#: includes/editor-templates/templates.php:166 assets/js/editor.js:8566
#: assets/js/editor.js:8579
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:112
#: assets/js/packages/editor-controls/editor-controls.strings.js:117
msgid "Move"
msgstr "Sposta"

#: includes/editor-templates/templates.php:317
#: includes/editor-templates/templates.php:408
msgid "Move to"
msgstr "Sposta in"

#: includes/editor-templates/templates.php:321
#: includes/editor-templates/templates.php:412
msgid "Copy to"
msgstr "Copia in"

#: includes/controls/url.php:68
#: modules/atomic-widgets/controls/types/link-control.php:23
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:76
msgid "Type or paste your URL"
msgstr "Digita o incolla il tuo URL"

#: app/modules/kit-library/module.php:135
#: core/frontend/render-mode-manager.php:152
msgid "Not Authorized"
msgstr "Non autorizzato"

#: modules/atomic-widgets/opt-in.php:42
msgid "Enable Editor V4."
msgstr "Abilita Editor V4."

#: modules/promotions/pointers/birthday.php:35
msgid "View Deals"
msgstr "Visualizza offerte"

#: includes/managers/elements.php:280
msgid "Atomic Elements"
msgstr "Elementi atomici"

#: includes/template-library/sources/cloud.php:305
msgid "You do not have permission to create preview documents."
msgstr "Non hai i permessi per creare documenti di anteprima."

#: modules/promotions/pointers/birthday.php:30
msgid "Elementor’s 9th Birthday sale!"
msgstr "Offerta per il 9° compleanno di Elementor!"

#: modules/cloud-library/module.php:143 assets/js/editor.js:9816
msgid "Then you can find all your templates in one convenient library."
msgstr "Così potrai trovare tutti i tuoi template in un'unica comoda libreria."

#: modules/promotions/pointers/birthday.php:31
msgid "Celebrate Elementor’s birthday with us—exclusive deals are available now."
msgstr "Festeggia il compleanno di Elementor con noi: sono disponibili offerte esclusive."

#: includes/editor-templates/templates.php:534
msgid "You’ve saved 100% of the templates in your plan."
msgstr "Hai salvato il 100% dei template nel tuo piano."

#. translators: %s is the "Upgrade now" link
#: includes/editor-templates/templates.php:538
msgid "To get more space %s"
msgstr "Per ottenere più spazio %s"

#: modules/cloud-library/connect/cloud-library.php:199
msgid "Failed to save preview."
msgstr "Impossibile salvare l'anteprima."

#: modules/cloud-library/connect/cloud-library.php:221
msgid "Failed to mark preview as failed."
msgstr "Impossibile contrassegnare l'anteprima come fallita."

#: modules/atomic-opt-in/module.php:23
msgid "Enable the settings Opt In page"
msgstr "Abilita la pagina Opt In delle impostazioni"

#: includes/editor-templates/templates.php:277
#: includes/editor-templates/templates.php:386
msgid "Upgrade to get more storage space or delete old templates to make room."
msgstr "Aggiorna per ottenere più spazio di archiviazione o elimina i vecchi template per fare spazio."

#: modules/variables/module.php:26
msgid "Enable variables. (For this feature to work - Atomic Widgets must be active)"
msgstr "Abilita le variabili. (Affinché questa funzionalità sia operativa, i widget atomici devono essere attivi)"

#: core/admin/admin-notices.php:536
msgid "Make sure your site has an accessibility statement page"
msgstr "Assicurati che il tuo sito abbia una pagina di dichiarazione di accessibilità"

#: includes/editor-templates/templates.php:528
msgid "Site Templates"
msgstr "Template del sito"

#: includes/editor-templates/templates.php:114
#: includes/editor-templates/templates.php:632
msgid "Site templates"
msgstr "Template del sito"

#: includes/widgets/progress.php:154
msgid "Display Title"
msgstr "Visualizza il titolo"

#: includes/editor-templates/templates.php:553
#: includes/editor-templates/templates.php:569
#: includes/editor-templates/templates.php:583
msgid "Learn more about the"
msgstr "Approfondisci"

#: modules/global-classes/global-classes-rest-api.php:177
msgid "Global classes limit exceeded. Maximum allowed: %d"
msgstr "Il limite delle classi globali è stato superato. Il massimo consentito è %d"

#: modules/atomic-opt-in/module.php:22
msgid "Editor v4 (Opt In Page)"
msgstr "Editor v4 (pagina Opt In)"

#: core/admin/admin-notices.php:537
msgid "Create a more inclusive site experience for all your visitors. With Ally, it's easy to add your statement page in just a few clicks."
msgstr "Crea un'esperienza di sito più inclusiva per tutti i tuoi visitatori. Con Ally è facile aggiungere la tua pagina di dichiarazione relativa all'Accessibilità del sito in pochi clic."

#: includes/editor-templates/templates.php:500
msgid "Cloud Templates"
msgstr "Cloud Templates"

#: modules/cloud-library/documents/cloud-template-preview.php:44
msgid "Cloud Template Previews"
msgstr "Anteprime di Cloud Template"

#: modules/cloud-library/documents/cloud-template-preview.php:40
msgid "Cloud Template Preview"
msgstr "Anteprima di Cloud Template"

#: modules/cloud-library/module.php:89
msgid "Cloud Templates and Website Templates empowers you to save and manage design elements across all your projects. This feature is associated and connected to your Elementor Pro account and can be accessed from any website associated with your account."
msgstr "Cloud Templates ti permette di salvare e gestire gli elementi di design in tutti i tuoi progetti. Questa funzionalità è associata e collegata al tuo account Elementor Pro e può essere accessibile da qualsiasi sito web associato al tuo account."

#: includes/editor-templates/templates.php:118
#: includes/editor-templates/templates.php:636
msgid "Cloud templates"
msgstr "Cloud Templates"

#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:32
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:65
msgid "Paragraph"
msgstr "Paragrafo"

#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:49
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:66
msgid "Type your paragraph here"
msgstr "Digita qui il tuo paragrafo"

#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:66
msgid "Button text"
msgstr "Testo del pulsante"

#: includes/template-library/sources/cloud.php:122
msgid "New Folder"
msgstr "Nuova cartella"

#: includes/template-library/sources/cloud.php:23
msgid "Cloud-Library is not instantiated."
msgstr "La libreria cloud non è stata istanziata."

#: includes/template-library/sources/cloud.php:36
#: modules/cloud-library/connect/cloud-library.php:15
#: modules/cloud-library/module.php:88
msgid "Cloud Library"
msgstr "Libreria cloud"

#: includes/editor-templates/templates.php:138
msgid "Create a New Folder"
msgstr "Crea una nuova cartella"

#: includes/editor-templates/templates.php:297
msgid "Open"
msgstr "Apri"

#: includes/settings/tools.php:314
msgid "Elementor Cache"
msgstr "Cache di Elementor"

#: includes/settings/tools.php:317 modules/admin-bar/module.php:148
msgid "Clear Files & Data"
msgstr "Cancella file e dati"

#: includes/controls/gallery.php:127 includes/controls/media.php:323
msgid "Connect Now"
msgstr "Connetti ora"

#: includes/controls/gallery.php:126 includes/controls/media.php:322
msgid "This image isn't optimized. You need to connect your Image Optimizer account first."
msgstr "Questa immagine non è ottimizzata. Devi prima connettere il tuo account di Image Optimizer."

#: includes/controls/media.php:307
msgid "Image size settings don’t apply to Dynamic Images."
msgstr "Le impostazioni delle dimensioni dell'immagine non si applicano alle immagini dinamiche."

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/floating-buttons/module.php:352 modules/landing-pages/module.php:236
msgid "Or view %1$sTrashed Items%2$s"
msgstr "Oppure visualizza gli %1$sElementi nel cestino%2$s"

#: includes/settings/tools.php:318
msgid "Clear outdated CSS files and cached data in the database (rendered HTML, JS/CSS assets, etc.). We'll regenerate those files the next time someone visits any page on your website."
msgstr "Cancella i file CSS obsoleti e i dati memorizzati nella cache del database (HTML renderizzato, risorse JS/CSS, ecc.). Rigenereremo questi file la prossima volta che qualcuno visiterà una qualsiasi pagina del tuo sito web."

#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:67
msgid "Type your button text here"
msgstr "Digita qui il testo del pulsante"

#: modules/nested-tabs/widgets/nested-tabs.php:182
msgid "Add Tab"
msgstr "Aggiungi scheda"

#: includes/widgets/video.php:438
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:85
msgid "Captions"
msgstr "Didascalie"

#: includes/template-library/sources/local.php:1766
msgid "Sorry, you are not allowed to do that."
msgstr "Non hai i permessi per farlo."

#: includes/elements/container.php:1401 includes/widgets/common-base.php:381
msgid "Grid Item"
msgstr "Elemento griglia"

#: modules/element-cache/module.php:140
msgid "Element Cache"
msgstr "Cache degli elementi"

#: includes/widgets/image-gallery.php:303
msgid "Custom Gap"
msgstr "Spaziatura personalizzata"

#: modules/ai/site-planner-connect/module.php:51
msgid "Approve & Connect"
msgstr "Approva e connetti"

#: modules/ai/site-planner-connect/module.php:49
msgid "Connect to Site Planner"
msgstr "Connettiti a Site Planner"

#: modules/ai/site-planner-connect/module.php:50
msgid "To connect your site to Site Planner, you need to generate an app password."
msgstr "Per connettere il tuo sito a Site Planner, devi generare una password per l'applicazione."

#: includes/managers/elements.php:298
msgid "Hello+"
msgstr "Hello+"

#: modules/atomic-widgets/elements/div-block/div-block.php:34
#: modules/library/documents/div-block.php:52 assets/js/editor.js:8485
msgid "Div Block"
msgstr "Blocco Div"

#: includes/elements/container.php:1453 includes/widgets/common-base.php:433
msgid "Row Span"
msgstr "Estensione della riga"

#: includes/elements/container.php:1409 includes/widgets/common-base.php:389
msgid "Column Span"
msgstr "Estensione della colonna"

#: modules/ai/module.php:225 modules/ai/module.php:260
msgid "Animate With AI"
msgstr "Anima con l'IA"

#: modules/global-classes/module.php:54
msgid "Global Classes"
msgstr "Classi globali"

#: modules/global-classes/module.php:55
msgid "Enable global CSS classes."
msgstr "Abilita classi CSS globali."

#: modules/ai/module.php:417
msgid "Image added successfully"
msgstr "Immagine aggiunta con successo"

#: modules/promotions/promotion-data.php:96
msgid "Adjust transitions and animations."
msgstr "Regola transizioni e animazioni."

#: includes/widgets/image-carousel.php:153
msgid "Carousel Name"
msgstr "Nome del carosello"

#: modules/promotions/promotion-data.php:45
msgid "Apply rotating effects to text."
msgstr "Applica effetti di rotazione al testo."

#: modules/promotions/promotion-data.php:78
msgid "Combine text, buttons, and images."
msgstr "Abbina testo, pulsanti e immagini."

#: includes/widgets/image-carousel.php:664
msgid "Space Between Dots"
msgstr "Spazio tra i punti"

#: modules/promotions/promotion-data.php:112
msgid "Display reviews in a rotating carousel."
msgstr "Visualizza le recensioni in un carosello rotante."

#: modules/promotions/promotion-data.php:95
msgid "Create flexible custom carousels."
msgstr "Crea caroselli personalizzati flessibili."

#: modules/promotions/promotion-data.php:62
msgid "Adjust layout and playback settings."
msgstr "Regola le impostazioni di layout e riproduzione."

#: modules/promotions/promotion-data.php:114
msgid "Customize layouts for visual appeal."
msgstr "Personalizza i layout per ottenere un effetto visivo."

#: modules/promotions/promotion-data.php:93
msgid "Design Custom Carousels"
msgstr "Crea caroselli personalizzati"

#: modules/promotions/promotion-data.php:80
msgid "Create unique, interactive designs."
msgstr "Crea design unici e interattivi."

#: modules/promotions/promotion-data.php:79
msgid "Add hover animations and CSS effects."
msgstr "Aggiungi animazioni al passaggio del mouse ed effetti CSS."

#: modules/promotions/promotion-data.php:63
msgid "Seamlessly customize video appearance."
msgstr "Personalizza perfettamente l'aspetto del video."

#: modules/promotions/promotion-data.php:44
msgid "Highlight key messages dynamically."
msgstr "Evidenzia i messaggi chiave in modo dinamico."

#: modules/promotions/promotion-data.php:113
msgid "Boost credibility with dynamic testimonials."
msgstr "Aumenta la credibilità con testimonianze dinamiche."

#: modules/promotions/promotion-data.php:110
msgid "Upgrade Your Testimonials"
msgstr "Aggiorna le tue testimonianze"

#: modules/promotions/promotion-data.php:97
msgid "Showcase multiple items with style."
msgstr "Mostra più elementi con stile."

#: modules/promotions/promotion-data.php:46
msgid "Fully customize your headlines."
msgstr "Personalizza completamente le tue intestazioni."

#: modules/ai/feature-intro/product-image-unification-intro.php:35
msgid "Now you can process images in bulk and standardized the background and ratio - no manual editing required!"
msgstr "Ora puoi elaborare immagini in blocco e standardizzare lo sfondo e la proporzione, senza bisogno di modifiche manuali!"

#: modules/promotions/promotion-data.php:61
msgid "Embed videos with full control."
msgstr "Incorpora i video con pieno controllo."

#: modules/promotions/promotion-data.php:76
msgid "Boost Conversions with CTAs"
msgstr "Aumenta le conversioni con gli inviti all'azione (CTA)"

#: modules/promotions/promotion-data.php:42
msgid "Bring Headlines to Life"
msgstr "Anima le intestazioni"

#: modules/ai/module.php:321 assets/js/ai-unify-product-images.js:16692
msgid "Unify with Elementor AI"
msgstr "Unisci con Elementor AI"

#: modules/ai/feature-intro/product-image-unification-intro.php:34
msgid "New! Unify pack-shots with Elementor AI"
msgstr "Nuovo! Unisci i pacchetti di foto con Elementor AI"

#: modules/promotions/promotion-data.php:59
msgid "Showcase Video Playlists"
msgstr "Mostra le playlist dei video"

#: modules/cloud-library/connect/cloud-library.php:340
msgid "Not connected"
msgstr "Non collegato"

#: core/admin/admin-notices.php:611
msgid "Use Elementor's Site Mailer to ensure your store emails like purchase confirmations, shipping updates and more are reliably delivered."
msgstr "Usa Site Mailer di Elementor per assicurarti che le email del tuo negozio, come le conferme d'acquisto, gli aggiornamenti sulle spedizioni e altro ancora, vengano recapitate in modo affidabile."

#: core/admin/admin-notices.php:610
msgid "Improve Transactional Email Deliverability"
msgstr "Migliora il tasso di consegna delle email transazionali"

#: core/admin/admin-notices.php:589
msgid "Ensure your form emails avoid the spam folder!"
msgstr "Assicurati che le email del tuo modulo evitino la cartella spam!"

#: core/admin/admin-notices.php:590
msgid "Use Site Mailer for improved email deliverability, detailed email logs, and an easy setup."
msgstr "Usa Site Mailer per migliorare il tasso di consegna delle email, per avere log dettagliati delle email e per semplificare la configurazione."

#: modules/checklist/steps/setup-header.php:70
msgid "Add a header"
msgstr "Aggiungi un header"

#: modules/checklist/steps/setup-header.php:62
msgid "Set up a header"
msgstr "Imposta un header"

#: modules/checklist/steps/add-logo.php:25
msgid "Add your logo"
msgstr "Aggiungi il tuo logo"

#: modules/checklist/steps/assign-homepage.php:31
msgid "Assign homepage"
msgstr "Assegna homepage"

#: modules/checklist/steps/assign-homepage.php:23
msgid "Assign a homepage"
msgstr "Assegna una homepage"

#: modules/checklist/steps/set-fonts-and-colors.php:27
msgid "Set up your Global Fonts & Colors"
msgstr "Imposta i tuoi font e colori globali"

#: modules/checklist/steps/add-logo.php:33
#: modules/checklist/steps/set-fonts-and-colors.php:35
msgid "Go to Site Identity"
msgstr "Vai a Denominazione del sito"

#: modules/checklist/steps/setup-header.php:66
msgid "This element applies across different pages, so visitors can easily navigate around your site."
msgstr "Questo elemento si applica a diverse pagine, in modo che i visitatori possano navigare facilmente all'interno del tuo sito."

#: modules/checklist/steps/assign-homepage.php:27
msgid "Before your launch, make sure to assign a homepage so visitors have a clear entry point into your site."
msgstr "Prima del lancio, assicurati di assegnare una homepage in modo che i visitatori abbiano un punto di ingresso chiaro nel tuo sito."

#: modules/checklist/steps/set-fonts-and-colors.php:31
msgid "Global colors and fonts ensure a cohesive look across your site. Start by defining one color and one font."
msgstr "I colori e i font globali assicurano un aspetto coerente in tutto il tuo sito. Inizia definendo un colore e un font."

#: modules/checklist/steps/add-logo.php:29
msgid "Let's start by adding your logo and filling in the site identity settings. This will establish your initial presence and also improve SEO."
msgstr "Inizia aggiungendo il tuo logo e compilando le impostazioni relative alla denominazione del sito. Questo è il primo passo per creare la tua presenza online e migliorare la SEO."

#: core/experiments/manager.php:344
msgid "Create advanced layouts and responsive designs with %1$sFlexbox%2$s and %3$sGrid%4$s container elements. Give it a try using the %5$sContainer playground%6$s."
msgstr "Crea layout avanzati e design responsive con gli elementi contenitore %1$sFlexbox%2$s e %3$sGriglia%4$s. Provali usando il %5$sContainer Playground%6$s."

#: modules/floating-buttons/base/widget-floating-bars-base.php:69
msgid "Banner"
msgstr "Banner"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:80
msgid "Tag"
msgstr "Tag"

#: modules/checklist/steps/create-pages.php:36
msgid "Create a new page"
msgstr "Crea una nuova pagina"

#: modules/checklist/steps/create-pages.php:28
msgid "Create your first 3 pages"
msgstr "Crea le tue prime 3 pagine"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1213
msgid "Element spacing"
msgstr "Spaziatura degli elementi"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1049
msgid "Horizontal position"
msgstr "Posizione orizzontale"

#: modules/floating-buttons/base/widget-floating-bars-base.php:339
msgid "Enter your text"
msgstr "Inserisci il tuo testo"

#: modules/floating-buttons/base/widget-floating-bars-base.php:157
msgid "Shop now"
msgstr "Acquista ora"

#: modules/floating-buttons/base/widget-floating-bars-base.php:156
#: modules/floating-buttons/base/widget-floating-bars-base.php:204
msgid "Enter text"
msgstr "Inserisci il testo"

#: modules/floating-buttons/base/widget-floating-bars-base.php:143
#: modules/floating-buttons/base/widget-floating-bars-base.php:555
msgid "CTA Button"
msgstr "Pulsante CTA"

#: modules/floating-buttons/base/widget-floating-bars-base.php:131
msgid "Enter your text here"
msgstr "Inserisci il tuo testo qui"

#: modules/floating-buttons/base/widget-floating-bars-base.php:105
#: modules/floating-buttons/base/widget-floating-bars-base.php:391
msgid "Announcement"
msgstr "Annuncio"

#: modules/ai/preferences.php:61
msgid "Elementor - AI"
msgstr "Elementor - AI"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1183
msgid "Align Elements"
msgstr "Allinea elementi"

#: core/debug/classes/shop-page-edit.php:23
msgid "Sorry, The content area was not been found on your page"
msgstr "L'area del contenuto non è stata trovata nella tua pagina"

#: modules/floating-buttons/documents/floating-buttons.php:203
#: modules/floating-buttons/module.php:342
msgid "Floating Element"
msgstr "Elemento fluttuante"

#: modules/floating-buttons/admin-menu-items/floating-buttons-menu-item.php:22
#: modules/floating-buttons/admin-menu-items/floating-buttons-menu-item.php:26
#: modules/floating-buttons/documents/floating-buttons.php:207
msgid "Floating Elements"
msgstr "Elementi fluttuanti"

#: modules/floating-buttons/module.php:47 assets/js/editor.js:51664
msgid "Floating Bars"
msgstr "Barre fluttuanti"

#: modules/floating-buttons/base/widget-floating-bars-base.php:219
#: modules/floating-buttons/base/widget-floating-bars-base.php:1173
msgid "Floating Bar"
msgstr "Barra fluttuante"

#: modules/floating-buttons/base/widget-floating-bars-base.php:199
msgid "Accessible Name"
msgstr "Nome accessibile"

#: modules/ai/preferences.php:73
msgid "Enable Elementor AI functionality"
msgstr "Abilita funzionalità Elementor AI"

#: modules/floating-buttons/base/widget-contact-button-base.php:466
#: modules/floating-buttons/base/widget-contact-button-base.php:722
msgid "Add accessible name"
msgstr "Aggiungi nome accessibile"

#: modules/floating-buttons/base/widget-contact-button-base.php:463
#: modules/floating-buttons/base/widget-contact-button-base.php:719
msgid "Accessible name"
msgstr "Nome accessibile"

#: modules/floating-buttons/base/widget-floating-bars-base.php:240
msgid "Pause Icon"
msgstr "Icona pausa"

#: modules/floating-buttons/base/widget-floating-bars-base.php:64
msgid "Just in! Cool summer tees"
msgstr "Appena arrivate! Fresche magliette estive"

#: modules/atomic-widgets/module.php:148
msgid "Atomic Widgets"
msgstr "Widget atomici"

#: modules/atomic-widgets/module.php:149
msgid "Enable atomic widgets."
msgstr "Abilita i widget atomici."

#: includes/admin-templates/new-floating-elements.php:21
msgid "Use floating elements to engage your visitors and increase conversions."
msgstr "Usa gli elementi fluttuanti per coinvolgere i tuoi visitatori e aumentare le conversioni."

#: includes/settings/settings.php:259
msgid "Tailor how Elementor enhances your site, from post types to other functions."
msgstr "Personalizza il modo in cui Elementor migliora il tuo sito, dai tipi di contenuto alle altre funzioni."

#. translators: %1$s Span open tag, %2$s: Span close tag.
#: includes/admin-templates/new-floating-elements.php:16
msgid "Floating Elements Help You %1$sWork Efficiently%2$s"
msgstr "Gli elementi fluttuanti ti aiutano a %1$slavorare in modo efficiente%2$s"

#: includes/settings/settings.php:336
msgid "Personalize the way Elementor works on your website by choosing the advanced features and how they operate."
msgstr "Personalizza il modo in cui Elementor agisce sul tuo sito web scegliendo le funzionalità avanzate e il modo in cui esse operano."

#: includes/settings/settings.php:466
msgid "Improve initial page load performance by lazy loading all background images except the first one."
msgstr "Migliora le prestazioni del caricamento iniziale della pagina caricando in lazy loading tutte le immagini in background, tranne la prima."

#: modules/floating-buttons/base/widget-floating-bars-base.php:315
msgid "Headlines"
msgstr "Intestazioni"

#: core/debug/classes/shop-page-edit.php:15
msgid "You are trying to edit the Shop Page although it is a Product Archive. Use the Theme Builder to create your Shop Archive template instead"
msgstr "Stai cercando di modificare la Pagina del negozio anche se si tratta di un Archivio di prodotti. Utilizza il Theme Builder per creare il template dell'Archivio del negozio"

#: includes/admin-templates/new-floating-elements.php:50
msgid "Create Floating Element"
msgstr "Crea un elemento fluttuante"

#: includes/admin-templates/new-floating-elements.php:31
msgid "Choose Floating Element"
msgstr "Scegli un elemento fluttuante"

#: modules/floating-buttons/widgets/floating-bars-var-1.php:25
msgid "Floating Bar CTA"
msgstr "Barra fluttuante di invito all'azione (CTA)"

#: modules/floating-buttons/base/widget-floating-bars-base.php:228
#: modules/floating-buttons/base/widget-floating-bars-base.php:1130
msgid "Pause and Play"
msgstr "Pausa e Riproduci"

#: modules/checklist/steps/create-pages.php:32
msgid "Jumpstart your creation with professional designs from the Template Library or start from scratch."
msgstr "Inizia la tua creazione con i design professionali della Libreria dei Template o parti da zero."

#: modules/floating-buttons/base/widget-floating-bars-base.php:1259
msgid "Headline"
msgstr "Titolo"

#: modules/link-in-bio/widgets/link-in-bio.php:28
msgid "Minimalist"
msgstr "Minimalista"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1545
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:138
msgid "Dimensions"
msgstr "Dimensioni"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:334
msgid "CTA link"
msgstr "Link CTA"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:285
#: assets/js/packages/editor-controls/editor-controls.js:28
#: assets/js/packages/editor-controls/editor-controls.strings.js:82
msgid "Add item"
msgstr "Aggiungi elemento"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:243
msgid "Images Per Row"
msgstr "Immagini per riga"

#: modules/floating-buttons/module.php:277
msgid "Entire Site"
msgstr "Intero sito"

#: modules/floating-buttons/documents/floating-buttons.php:195
msgid "Set as Entire Site"
msgstr "Imposta come Intero sito"

#: modules/floating-buttons/documents/floating-buttons.php:188
msgid "Remove From Entire Site"
msgstr "Rimuovi dall'Intero sito"

#: modules/floating-buttons/documents/floating-buttons.php:32
msgid "After publishing this widget, you will be able to set it as visible on the entire site in the Admin Table."
msgstr "Dopo aver pubblicato questo widget, puoi impostarlo come visibile sull'intero sito nella tabella di amministrazione."

#: modules/link-in-bio/base/widget-link-in-bio-base.php:215
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1026
msgid "Image Links"
msgstr "Link alle immagini"

#: modules/floating-buttons/module.php:191
msgid "Instances"
msgstr "Istanze"

#: modules/floating-buttons/module.php:188
msgid "Click Tracking"
msgstr "Tracciamento dei clic"

#: modules/floating-buttons/widgets/contact-buttons.php:26
msgid "Single Chat"
msgstr "Chat singola"

#: modules/floating-buttons/module.php:344
msgid "Add a Floating element so your users can easily get in touch!"
msgstr "Aggiungi un elemento fluttuante per consentire ai tuoi utenti di mettersi in contatto facilmente!"

#: modules/floating-buttons/base/widget-contact-button-base.php:3082
#: modules/floating-buttons/base/widget-floating-bars-base.php:1488
msgid "CSS"
msgstr "CSS"

#: modules/floating-buttons/base/widget-contact-button-base.php:2481
#: modules/floating-buttons/base/widget-contact-button-base.php:2501
msgid "Text and Icon Color"
msgstr "Colore testo e icona"

#: modules/floating-buttons/base/widget-contact-button-base.php:2440
msgid "Link Spacing"
msgstr "Spaziatura link"

#: modules/floating-buttons/base/widget-contact-button-base.php:2107
msgid "Tooltips"
msgstr "Tooltip"

#: modules/floating-buttons/base/widget-contact-button-base.php:828
msgid "Enter description"
msgstr "Inserisci descrizione"

#: modules/floating-buttons/base/widget-contact-button-base.php:552
msgid "Enter the text"
msgstr "Inserisci il testo"

#: modules/floating-buttons/base/widget-contact-button-base.php:813
msgid "Enter title"
msgstr "Inserisci titolo"

#: modules/floating-buttons/base/widget-contact-button-base.php:146
msgid "Tooltip"
msgstr "Tooltip"

#: modules/floating-buttons/base/widget-contact-button-base.php:86
msgid "Call now"
msgstr "Chiama ora"

#: includes/widgets/image-box.php:409 includes/widgets/image.php:386
msgid "Scale Down"
msgstr "Riduci"

#: core/document-types/page-base.php:188
msgid "No %s found."
msgstr "Nessun %s trovato."

#: core/document-types/page-base.php:187
msgid "Search %s"
msgstr "Cerca %s"

#: core/document-types/page-base.php:185
msgid "New %s"
msgstr "Nuovo %s"

#: core/document-types/page-base.php:189
msgid "No %s found in Trash."
msgstr "Nessun %s trovato nel cestino."

#: modules/announcements/module.php:110
msgid "Discover your new superpowers "
msgstr "Scopri i tuoi nuovi superpoteri "

#: modules/floating-buttons/base/widget-contact-button-base.php:3069
#: modules/floating-buttons/base/widget-floating-bars-base.php:1475
msgid "Responsive visibility will take effect only on preview mode or live page, and not while editing in Elementor."
msgstr "La visibilità responsive avrà effetto solo sulla modalità anteprima o sulla pagina live, e non durante la modifica in Elementor."

#: modules/floating-buttons/base/widget-contact-button-base.php:3045
msgid "Full Width on Mobile"
msgstr "Larghezza piena sui dispositivi mobile"

#: modules/floating-buttons/base/widget-contact-button-base.php:405
#: modules/floating-buttons/base/widget-contact-button-base.php:967
#: modules/link-in-bio/base/widget-link-in-bio-base.php:516
#: modules/link-in-bio/base/widget-link-in-bio-base.php:762
msgid "Paste Waze link"
msgstr "Incolla il link Waze"

#: modules/floating-buttons/module.php:46 assets/js/editor.js:51666
#: assets/js/editor.js:51668
msgid "Floating Buttons"
msgstr "Pulsanti fluttuanti"

#: modules/floating-buttons/base/widget-contact-button-base.php:537
msgid "Call to Action"
msgstr "Invito all'azione"

#: modules/floating-buttons/base/widget-contact-button-base.php:536
msgid "Contact Details"
msgstr "Dettagli di contatto"

#: modules/floating-buttons/base/widget-contact-button-base.php:532
msgid "Display Text"
msgstr "Visualizza il testo"

#: modules/floating-buttons/base/widget-contact-button-base.php:546
#: modules/floating-buttons/base/widget-contact-button-base.php:734
#: modules/floating-buttons/base/widget-contact-button-base.php:1876
msgid "Call to Action Text"
msgstr "Testo invito all'azione"

#: modules/floating-buttons/base/widget-contact-button-base.php:691
msgid "Typing Animation"
msgstr "Animazione della digitazione"

#: modules/floating-buttons/base/widget-contact-button-base.php:626
msgid "Active Dot"
msgstr "Punto attivo"

#: modules/floating-buttons/base/widget-contact-button-base.php:516
msgid "Notification Dot"
msgstr "Punto di notifica"

#: includes/editor-templates/panel.php:131
msgid "Copy and Share Link"
msgstr "Copia e condividi il link"

#: modules/floating-buttons/base/widget-contact-button-base.php:1487
msgid "Hover animation is <b>desktop only</b>"
msgstr "L'animazione al passaggio del mouse è <b>solo sui desktop</b>"

#: modules/editor-events/module.php:48
msgid "Elementor Editor Events"
msgstr "Eventi dell'editor di Elementor"

#: modules/floating-buttons/base/widget-contact-button-base.php:2152
msgid "Button Bar"
msgstr "Barra dei pulsanti"

#: modules/floating-buttons/classes/render/contact-buttons-core-render.php:58
msgid "Links window"
msgstr "Finestra dei link"

#: core/document-types/page-base.php:181
msgid "All %s"
msgstr "Tutti %s"

#. translators: 1: `<a>` opening tag, 2: `</a>` closing tag.
#: includes/widgets/video.php:364
msgid "Note: Autoplay is affected by %1$s Google’s Autoplay policy %2$s on Chrome browsers."
msgstr "Nota: la riproduzione automatica è influenzata dal %1$s criterio di riproduzione automatica di Google %2$s sui browser Chrome."

#: core/kits/documents/tabs/settings-background.php:75
msgid "Overscroll Behavior"
msgstr "Comportamento dello scorrimento eccessivo"

#: modules/floating-buttons/base/widget-contact-button-base.php:2256
msgid "Resource Links"
msgstr "Link alle risorse"

#: modules/floating-buttons/base/widget-contact-button-base.php:1830
msgid "Bubble Background Color"
msgstr "Colore di sfondo della bolla"

#: modules/announcements/module.php:111
msgid "<p>With AI for text, code, image generation and editing, you can bring your vision to life faster than ever. Start your free trial now - <b>no credit card required!</b></p>"
msgstr "<p>Con l'IA per la generazione e la modifica di testo, codice e immagini, puoi dare vita alla tua visione più velocemente che mai. Inizia subito la tua prova gratuita - <b>non è necessaria la carta di credito!</b></p>"

#: modules/floating-buttons/base/widget-contact-button-base.php:736
msgid "Start conversation:"
msgstr "Inizia la conversazione:"

#: modules/editor-events/module.php:49
msgid "Editor events processing"
msgstr "Elaborazione in corso degli eventi dell'editor"

#: modules/floating-buttons/base/widget-contact-button-base.php:2390
msgid "Info Links"
msgstr "Link alle informazioni"

#: includes/controls/repeater.php:194
msgid "In a Repeater control, if you specify a minimum number of items, you must also specify a default value that contains at least that number of items."
msgstr "In un controllo ripetitore (repeater), se si specifica un numero minimo di elementi, è necessario specificare anche un valore predefinito che contenga almeno quel numero di elementi."

#. translators: %s: <head> tag.
#: includes/settings/settings.php:423
msgid "Internal Embedding places all CSS in the %s which works great for troubleshooting, while External File uses external CSS file for better performance (recommended)."
msgstr "\"Incorporato internamente\" colloca tutti i CSS in %s, il che funziona bene per la risoluzione dei problemi, mentre \"File esterno\" utilizza un file CSS esterno per migliorare le prestazioni (consigliato)."

#: modules/floating-buttons/base/widget-contact-button-base.php:2226
msgid "Adjust transition duration to change the speed of the <b>hover animation on desktop</b> and the <b>click animation on touchscreen</b>."
msgstr "Regola la durata della transizione per modificare la velocità dell'<b>animazione al passaggio del mouse su desktop</b> e dell'<b>animazione al clic sugli screen touch</b>."

#: modules/floating-buttons/base/widget-contact-button-base.php:2056
msgid "Buttons Spacing"
msgstr "Spaziatura tra i pulsanti"

#: modules/element-cache/module.php:155
msgid "1 Year"
msgstr "1 anno"

#: modules/element-cache/module.php:154
msgid "1 Month"
msgstr "1 mese"

#: modules/element-cache/module.php:153
msgid "2 Weeks"
msgstr "2 settimane"

#: modules/element-cache/module.php:152
msgid "1 Week"
msgstr "1 settimana"

#: modules/element-cache/module.php:151
msgid "3 Days"
msgstr "3 giorni"

#: modules/element-cache/module.php:150
msgid "1 Day"
msgstr "1 giorno"

#: modules/element-cache/module.php:149
msgid "12 Hours"
msgstr "12 ore"

#: modules/element-cache/module.php:148
msgid "6 Hours"
msgstr "6 ore"

#: modules/element-cache/module.php:147
msgid "1 Hour"
msgstr "1 ora"

#: modules/element-cache/module.php:119
msgid "Cache Settings"
msgstr "Impostazioni cache"

#: modules/element-cache/module.php:46
msgid "Element Caching"
msgstr "Cache degli elementi"

#: includes/managers/elements.php:306
msgid "Link In Bio"
msgstr "Link In Bio"

#: core/base/traits/shared-widget-controls-trait.php:107
msgid "Icons Per Row"
msgstr "Icone per riga"

#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:222
msgid "Powered by Elementor"
msgstr "Powered by Elementor"

#: core/base/providers/social-network-provider.php:240
msgid "Skype"
msgstr "Skype"

#: core/base/providers/social-network-provider.php:234
msgid "Viber"
msgstr "Viber"

#: core/base/providers/social-network-provider.php:228
msgid "SMS"
msgstr "SMS"

#: core/base/providers/social-network-provider.php:198
msgid "Messenger"
msgstr "Messenger"

#: core/base/providers/social-network-provider.php:192
msgid "Waze"
msgstr "Waze"

#: core/base/providers/social-network-provider.php:180
msgid "Dribbble"
msgstr "Dribbble"

#: core/base/providers/social-network-provider.php:174
msgid "Behance"
msgstr "Behance"

#: core/base/providers/social-network-provider.php:162
msgid "Spotify"
msgstr "Spotify"

#: core/base/providers/social-network-provider.php:156
msgid "Apple Music"
msgstr "Apple Music"

#: core/base/providers/social-network-provider.php:150
msgid "WhatsApp"
msgstr "WhatsApp"

#: core/base/providers/social-network-provider.php:144
msgid "TikTok"
msgstr "TikTok"

#: core/base/providers/social-network-provider.php:132
msgid "Pinterest"
msgstr "Pinterest"

#: core/base/providers/social-network-provider.php:126
msgid "LinkedIn"
msgstr "LinkedIn"

#: core/base/providers/social-network-provider.php:120
msgid "Instagram"
msgstr "Instagram"

#: core/base/providers/social-network-provider.php:114
msgid "X (Twitter)"
msgstr "X (Twitter)"

#: core/base/providers/social-network-provider.php:108
msgid "Facebook"
msgstr "Facebook"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1738
msgid "Bottom Border"
msgstr "Bordo inferiore"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1034
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1710
msgid "Image Height"
msgstr "Altezza immagine"

#: modules/floating-buttons/base/widget-contact-button-base.php:2525
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1199
msgid "Dividers"
msgstr "Divisori"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:977
msgid "Profile"
msgstr "Profilo"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:973
msgid "Image style"
msgstr "Stile immagine"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:914
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1247
msgid "Identity"
msgstr "Identità"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:886
msgid "About Me"
msgstr "Chi sono"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:864
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1323
msgid "Title or Tagline"
msgstr "Titolo o motto"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:855
msgid "Sara Parker"
msgstr "Sara Parker"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1612
msgid "Full Screen Height"
msgstr "Altezza schermo intero"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1565
msgid "Layout Width"
msgstr "Larghezza layout"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:800
msgid "Add Icon"
msgstr "Aggiungi icona"

#: modules/floating-buttons/base/widget-contact-button-base.php:2264
#: modules/link-in-bio/base/widget-link-in-bio-base.php:559
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1384
#: assets/js/app.js:6344
msgid "Icons"
msgstr "Icone"

#: modules/floating-buttons/base/widget-contact-button-base.php:920
#: modules/link-in-bio/base/widget-link-in-bio-base.php:534
#: modules/link-in-bio/base/widget-link-in-bio-base.php:783
msgid "Enter your username"
msgstr "Inserisci il tuo nome utente"

#: modules/floating-buttons/base/widget-contact-button-base.php:838
#: modules/link-in-bio/base/widget-link-in-bio-base.php:440
#: modules/link-in-bio/base/widget-link-in-bio-base.php:682
msgid "Enter your email"
msgstr "Inserisci la tua email"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:356
msgid "Link Type"
msgstr "Tipo di link"

#: modules/floating-buttons/base/widget-contact-button-base.php:737
#: modules/floating-buttons/base/widget-contact-button-base.php:1047
msgid "Type your text here"
msgstr "Digita qui il tuo testo"

#: modules/floating-buttons/base/widget-contact-button-base.php:682
msgid "14:20"
msgstr "14:20"

#: modules/floating-buttons/base/widget-contact-button-base.php:681
msgid "2:20 PM"
msgstr "2:20 PM"

#: modules/floating-buttons/base/widget-contact-button-base.php:677
msgid "Time format"
msgstr "Formato ora"

#: modules/floating-buttons/base/widget-contact-button-base.php:655
msgid "Rob"
msgstr "Rob"

#: modules/floating-buttons/base/widget-contact-button-base.php:613
#: modules/floating-buttons/base/widget-contact-button-base.php:1532
msgid "Profile Image"
msgstr "Immagine del profilo"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:69
#: modules/floating-buttons/base/widget-contact-button-base.php:131
msgid "Type your title here"
msgstr "Digita qui il tuo titolo"

#: modules/floating-buttons/base/widget-contact-button-base.php:123
#: modules/floating-buttons/base/widget-contact-button-base.php:656
msgid "Type your name here"
msgstr "Digita qui il tuo nome"

#: modules/floating-buttons/base/widget-contact-button-base.php:122
msgid "Rob Jones"
msgstr "Rob Jones"

#: modules/floating-buttons/base/widget-contact-button-base.php:380
#: modules/floating-buttons/base/widget-contact-button-base.php:982
msgid "Action"
msgstr "Azione"

#: modules/floating-buttons/base/widget-contact-button-base.php:342
#: modules/floating-buttons/base/widget-contact-button-base.php:896
#: modules/link-in-bio/base/widget-link-in-bio-base.php:737
msgid "+"
msgstr "+"

#: modules/floating-buttons/base/widget-contact-button-base.php:321
#: modules/floating-buttons/base/widget-contact-button-base.php:663
#: modules/floating-buttons/base/widget-contact-button-base.php:670
#: modules/floating-buttons/base/widget-contact-button-base.php:872
#: modules/floating-buttons/base/widget-contact-button-base.php:874
#: modules/floating-buttons/base/widget-contact-button-base.php:1768
#: modules/link-in-bio/base/widget-link-in-bio-base.php:465
#: modules/link-in-bio/base/widget-link-in-bio-base.php:476
#: modules/link-in-bio/base/widget-link-in-bio-base.php:716
#: modules/link-in-bio/base/widget-link-in-bio-base.php:718
msgid "Message"
msgstr "Messaggio"

#: modules/floating-buttons/base/widget-contact-button-base.php:305
#: modules/floating-buttons/base/widget-contact-button-base.php:857
#: modules/floating-buttons/base/widget-contact-button-base.php:859
#: modules/link-in-bio/base/widget-link-in-bio-base.php:447
#: modules/link-in-bio/base/widget-link-in-bio-base.php:458
#: modules/link-in-bio/base/widget-link-in-bio-base.php:701
#: modules/link-in-bio/base/widget-link-in-bio-base.php:703
msgid "Subject"
msgstr "Oggetto"

#: modules/floating-buttons/base/widget-contact-button-base.php:294
msgid "@"
msgstr "@"

#: core/base/providers/social-network-provider.php:210
#: modules/floating-buttons/base/widget-contact-button-base.php:285
#: modules/floating-buttons/base/widget-contact-button-base.php:836
#: modules/link-in-bio/base/widget-link-in-bio-base.php:680
msgid "Email"
msgstr "Email"

#: modules/floating-buttons/base/widget-contact-button-base.php:479
#: modules/floating-buttons/base/widget-contact-button-base.php:779
#: modules/link-in-bio/base/widget-link-in-bio-base.php:598
msgid "Platform"
msgstr "Piattaforma"

#: modules/apps/admin-apps-page.php:126
msgid "Cannot Install"
msgstr "Impossibile installare"

#: modules/apps/admin-apps-page.php:119 modules/apps/admin-apps-page.php:150
msgid "Cannot Activate"
msgstr "Impossibile attivare"

#: includes/widgets/traits/button-trait.php:300
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:163
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:209
msgid "Space between"
msgstr "Spazio intermedio"

#: includes/settings/settings.php:407
msgid "Improve loading times on your site by selecting the optimization tools that best fit your requirements."
msgstr "Migliora i tempi di caricamento del tuo sito selezionando gli strumenti di ottimizzazione più adatti alle tue esigenze. "

#: core/settings/editor-preferences/model.php:212
msgid "Decide where you want to go when leaving the editor."
msgstr "Decidi dove vuoi andare quando lasci l'editor."

#: core/settings/editor-preferences/model.php:188
#: modules/styleguide/module.php:129
msgid "Temporarily overlay the canvas with the style guide to preview your changes to global colors and fonts."
msgstr "Sovrapponi temporaneamente l'area di lavoro con la guida di stile per vedere l'anteprima delle modifiche ai colori e ai font globali."

#: core/settings/editor-preferences/model.php:183
#: modules/styleguide/module.php:127
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:441
msgid "Show global settings"
msgstr "Mostra impostazioni globali"

#: core/settings/editor-preferences/model.php:144
msgid "This refers to elements you’ve hidden in the Responsive Visibility settings."
msgstr "Si riferisce agli elementi nascosti nelle impostazioni di Visibilità responsive."

#: core/base/providers/social-network-provider.php:204
msgid "Telephone"
msgstr "Telefono"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:495
msgid "Enter your number"
msgstr "Inserisci il tuo numero"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:419
#: modules/link-in-bio/base/widget-link-in-bio-base.php:650
msgid "Enter your link"
msgstr "Inserisci il tuo link"

#: modules/floating-buttons/base/widget-contact-button-base.php:2081
#: modules/floating-buttons/base/widget-contact-button-base.php:2172
#: modules/floating-buttons/base/widget-contact-button-base.php:2865
#: modules/floating-buttons/base/widget-floating-bars-base.php:840
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1159
msgid "Corners"
msgstr "Angoli"

#: modules/floating-buttons/base/widget-contact-button-base.php:669
msgid "Hey, how can I help you today?"
msgstr "Ciao! Come posso aiutarti oggi?"

#: core/base/providers/social-network-provider.php:102
msgid "Save contact (vCard)"
msgstr "Salva contatto (vCard)"

#. translators: 1: fetchpriority attribute, 2: lazy loading attribute.
#: includes/settings/settings.php:439
msgid "Improve performance by applying %1$s on LCP image and %2$s on images below the fold."
msgstr "Migliora le prestazioni applicando %1$s all'immagine LCP e %2$s alle immagini \"sotto la piega\"."

#: includes/settings/settings.php:454
msgid "Reduce unnecessary render-blocking loads by dequeuing unused Gutenberg block editor scripts and styles."
msgstr "Riduci i carichi inutili che bloccano il rendering rimuovendo dalla coda gli script e gli stili inutilizzati dell'editor a blocchi di Gutenberg."

#: modules/link-in-bio/base/widget-link-in-bio-base.php:52
msgid "Kitchen Chronicles"
msgstr "Cronache di cucina"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:91
msgid "Top 10 Recipes"
msgstr "Le 10 migliori ricette"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:94
msgid "Meal Prep"
msgstr "Preparazione dei pasti"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:841
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1287
msgid "Bio"
msgstr "Bio"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:55
msgid "Join me on my journey to a healthier lifestyle"
msgstr "Unisciti a me nel mio viaggio verso uno stile di vita più sano"

#: modules/floating-buttons/base/widget-contact-button-base.php:147
#: modules/link-in-bio/base/widget-link-in-bio-base.php:590
msgid "Enter icon text"
msgstr "Inserisci il testo dell'icona"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:335
msgid "Enter link text"
msgstr "Inserisci il testo del link"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:429
msgid "Mail"
msgstr "Email"

#: modules/floating-buttons/base/widget-contact-button-base.php:186
msgid "Click to start chat"
msgstr "Fai clic per iniziare la chat"

#: modules/floating-buttons/base/widget-contact-button-base.php:65
msgid "Chat Button"
msgstr "Pulsante chat"

#: modules/floating-buttons/base/widget-contact-button-base.php:2087
#: modules/floating-buttons/base/widget-contact-button-base.php:2178
#: modules/floating-buttons/base/widget-contact-button-base.php:2871
#: modules/floating-buttons/base/widget-floating-bars-base.php:846
#: modules/link-in-bio/base/widget-link-in-bio-base.php:120
msgid "Sharp"
msgstr "Nitido"

#: modules/floating-buttons/base/widget-contact-button-base.php:247
msgid "Chat Box"
msgstr "Casella di chat"

#: modules/floating-buttons/base/widget-contact-button-base.php:1643
#: modules/floating-buttons/base/widget-floating-bars-base.php:295
#: modules/floating-buttons/base/widget-floating-bars-base.php:1036
msgid "Close Button"
msgstr "Pulsante di chiusura"

#: modules/floating-buttons/base/widget-contact-button-base.php:1657
msgid "Close Button Color"
msgstr "Colore del pulsante di chiusura"

#: modules/floating-buttons/base/widget-contact-button-base.php:183
msgid "Send Button"
msgstr "Pulsante di invio"

#: modules/floating-buttons/base/widget-contact-button-base.php:130
msgid "Store Manager"
msgstr "Responsabile del negozio"

#: modules/floating-buttons/base/widget-contact-button-base.php:2776
msgid "Close Animation"
msgstr "Animazione di chiusura"

#: modules/floating-buttons/base/widget-contact-button-base.php:2766
msgid "Open Animation"
msgstr "Animazione di apertura"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:88
msgid "Get Healthy"
msgstr "Rimanere in salute"

#. translators: %s: `<br>` tag.
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:25
msgid "Create Forms and Collect Leads %s with Elementor Pro"
msgstr "Crea moduli e raccogli lead %s con Elementor Pro"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1629
msgid "Apply Full Screen Height on"
msgstr "Applica altezza schermo intero su"

#: modules/floating-buttons/base/widget-contact-button-base.php:1799
msgid "Time"
msgstr "Orario"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:97
msgid "Healthy Living Resources"
msgstr "Risorse per una vita sana"

#: modules/floating-buttons/base/widget-contact-button-base.php:645
#: modules/floating-buttons/base/widget-contact-button-base.php:1714
msgid "Message Bubble"
msgstr "Messaggio a bolla"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:885
msgid "About"
msgstr "Informazioni su"

#: modules/element-cache/module.php:157
msgid "Specify the duration for which data is stored in the cache. Elements caching speeds up loading by serving pre-rendered copies of elements, rather than rendering them fresh each time. This control ensures efficient performance and up-to-date content."
msgstr "Specifica la durata della memorizzazione dei dati nella cache. La cache degli elementi accelera il caricamento mostrando copie prerenderizzate degli elementi, anziché renderizzarli ogni volta. Questo controllo garantisce prestazioni efficienti e contenuti aggiornati."

#: modules/floating-buttons/base/widget-contact-button-base.php:141
msgid "Contact Buttons"
msgstr "Pulsanti di contatto"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1674
msgid "Image Shape"
msgstr "Forma dell'immagine"

#: modules/element-cache/module.php:48
msgid "Elements caching reduces loading times by serving up a copy of an element instead of rendering it fresh every time the page is loaded. When active, Elementor will determine which elements can benefit from static loading - but you can override this."
msgstr "La cache degli elementi riduce i tempi di caricamento mostrando una copia di un elemento invece di fare il rendering ex novo ogni volta che la pagina viene caricata. Quando è attiva, Elementor determina quali elementi possono beneficiare del caricamento statico, ma è possibile annullarla."

#: modules/link-in-bio/base/widget-link-in-bio-base.php:303
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1075
msgid "CTA Link Buttons"
msgstr "Pulsanti con link CTA"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:545
msgid "Add CTA Link"
msgstr "Aggiungi un link CTA"

#: core/base/providers/social-network-provider.php:222
msgid "File Download"
msgstr "Scarica il file"

#. translators: %s: `<br>` tag.
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:25
msgid "Enjoy creative freedom %s with Custom Icons"
msgstr "Libera la tua creatività %s con le icone personalizzate"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:880
msgid "About Heading"
msgstr "Titolo della pagina About (Chi siamo)"

#: core/settings/editor-preferences/model.php:132
msgid "This only applies while you’re working in the editor. The front end won’t be affected."
msgstr "Questo vale solo quando si lavora nell'editor. Il frontend non ne è interessato."

#: core/settings/editor-preferences/model.php:116
msgid "Show quick edit options"
msgstr "Mostra opzioni di modifica rapida"

#: core/settings/editor-preferences/model.php:74
msgid "Dark mode"
msgstr "Modalità scura"

#: core/settings/editor-preferences/model.php:70
msgid "Light mode"
msgstr "Modalità chiara"

#: core/settings/editor-preferences/model.php:66
msgid "Display mode"
msgstr "Modalità di visualizzazione"

#: core/settings/editor-preferences/model.php:58
msgid "Panel"
msgstr "Pannello"

#: core/settings/editor-preferences/model.php:139
msgid "Show hidden elements"
msgstr "Mostra gli elementi nascosti"

#: core/admin/admin.php:1022 includes/controls/gallery.php:122
#: includes/controls/media.php:318
msgid "Optimize your images to enhance site performance by using Image Optimizer."
msgstr "Ottimizza le tue immagini per migliorare le prestazioni del sito utilizzando Image Optimizer."

#: core/experiments/manager.php:369
msgid "Optimized Markup"
msgstr "Markup ottimizzato"

#: core/experiments/manager.php:371
msgid "Reduce the DOM size by eliminating HTML tags in various elements and widgets. This experiment includes markup changes so it might require updating custom CSS/JS code and cause compatibility issues with third party plugins."
msgstr "Riduce le dimensioni del DOM eliminando i tag HTML in vari elementi e widget. Questo esperimento include modifiche al markup, pertanto potrebbe richiedere l'aggiornamento del codice CSS/JS personalizzato e causare problemi di compatibilità con plugin di terze parti."

#: core/settings/editor-preferences/model.php:127
msgid "Expand images in lightbox"
msgstr "Espandi le immagini nella lightbox"

#: core/settings/editor-preferences/model.php:120
msgid "Show additional actions while hovering over the handle of an element."
msgstr "Mostra azioni aggiuntive quando passi il mouse su un elemento."

#: core/settings/editor-preferences/model.php:83
msgid "Set light or dark mode, or auto-detect to sync with your operating system settings."
msgstr "Imposta la modalità chiara o scura, oppure auto-detect per la sincronizzazione con le impostazioni del sistema operativo."

#: core/admin/admin.php:349
msgid "Discounted Upgrades Now!"
msgstr "Aggiornamenti scontati ora!"

#: modules/promotions/admin-menu-items/go-pro-promotion-item.php:33
msgid "Upgrade Sale Now"
msgstr "La versione Pro ora in offerta!"

#: core/admin/admin-notices.php:709
msgid "Automatically compress and optimize images, resize larger files, or convert to WebP. Optimize images individually, in bulk, or on upload."
msgstr "Comprimi e ottimizza automaticamente le immagini, ridimensiona i file più grandi o convertili in WebP. Ottimizza le immagini singolarmente, massivamente o al momento del caricamento."

#: core/admin/admin-notices.php:708
msgid "Speed up your website with Image Optimizer by Elementor"
msgstr "Velocizza il tuo sito web con Image Optimizer di Elementor"

#: modules/home/<USER>
msgid "Default Elementor menu page."
msgstr "Pagina del menu di Elementor predefinita."

#: modules/home/<USER>
msgid "Elementor Home Screen"
msgstr "Schermata home di Elementor"

#: includes/widgets/counter.php:424
msgid "Number Alignment"
msgstr "Allineamento numero"

#: includes/widgets/counter.php:388
msgid "Number Position"
msgstr "Posizione numero"

#: includes/widgets/counter.php:343
msgid "Title Vertical Alignment"
msgstr "Allineamento verticale titolo"

#: includes/widgets/counter.php:314
msgid "Title Horizontal Alignment"
msgstr "Allineamento orizzontale titolo"

#: includes/widgets/counter.php:275
msgid "Title Position"
msgstr "Posizione titolo"

#: includes/settings/settings.php:215
msgid "Home"
msgstr "Home"

#: elementor.php:96
msgid "Elementor isn’t running because WordPress is outdated."
msgstr "Elementor non funziona perché WordPress è obsoleto."

#. translators: %s: PHP version.
#. translators: %s: WordPress version.
#: elementor.php:75 elementor.php:99
msgid "Update to version %s and get back to creating!"
msgstr "Aggiorna alla versione %s e torna a creare!"

#: elementor.php:72
msgid "Elementor isn’t running because PHP is outdated."
msgstr "Elementor non funziona perché PHP è obsoleto."

#: core/files/uploads-manager.php:589
msgid "You do not have permission to upload JSON files."
msgstr "Non hai i permessi per caricare file JSON."

#: includes/widgets/counter.php:452
msgid "Number Gap"
msgstr "Spaziatura tra i numeri"

#: includes/widgets/counter.php:372
msgid "Title Gap"
msgstr "Spaziatura del titolo"

#: core/admin/admin.php:1024 modules/apps/admin-apps-page.php:123
msgid "Install"
msgstr "Installa"

#: core/admin/admin.php:1024
msgid "Image Optimizer"
msgstr "Image Optimizer"

#: modules/shapes/widgets/text-path.php:149
msgid "Want to create custom text paths with SVG?"
msgstr "Vuoi creare percorsi di testo personalizzati con SVG?"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:37
msgid "Add any icon, anywhere on your website"
msgstr "Aggiungi qualsiasi icona, in qualsiasi punto del tuo sito web"

#. translators: %s: `<br>` tag.
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:34
msgid "Expand your icon library beyond FontAwesome and add icon %s libraries of your choice"
msgstr "Espandi la tua libreria di icone oltre FontAwesome e aggiungi librerie di icone %s di tua scelta"

#. translators: %s: br
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:31
msgid "Remain GDPR compliant with Custom Fonts that let you disable %s Google Fonts from your website"
msgstr "Resta conforme al GDPR con i Font Personalizzati che consentono di disabilitare %s Google Fonts dal tuo sito web"

#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:28
msgid "Upload any font to keep your website true to your brand"
msgstr "Carica qualsiasi font per mantenere il sito web fedele al tuo marchio"

#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:23
msgid "Stay on brand with a Custom Font"
msgstr "Resta in linea con il marchio con un Font Personalizzato"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:30
msgid "Leverage Elementor AI to instantly generate Custom Code for Elementor"
msgstr "Sfrutta Elementor AI per generare istantaneamente codice personalizzato per Elementor"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:29
msgid "Use Custom Code to create sophisticated custom interactions to engage visitors"
msgstr "Utilizza il codice personalizzato per creare interazioni personalizzate e sofisticate per coinvolgere i visitatori"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:28
msgid "Add Custom Code snippets anywhere on your website, including the header or footer to measure your page’s performance*"
msgstr "Aggiungi snippet di codice personalizzati in qualsiasi punto del tuo sito web, anche nell'header o nel footer, per misurare le prestazioni della tua pagina*"

#: modules/site-navigation/rest-fields/page-user-can.php:28
msgid "Whether the current user can edit or delete this post"
msgstr "Se l'utente corrente può modificare o eliminare questo articolo"

#: modules/apps/module.php:70
msgid "For Elementor"
msgstr "Per Elementor"

#: modules/apps/admin-pointer.php:35
msgid "Explore Add-ons"
msgstr "Esplora gli add-on"

#: modules/apps/admin-pointer.php:29
msgid "New! Popular Add-ons"
msgstr "Nuovo! Add-on popolari"

#: modules/apps/admin-menu-apps.php:22 modules/apps/admin-menu-apps.php:26
#: modules/apps/module.php:37 assets/js/admin-top-bar.js:183
#: assets/js/editor.js:38612
msgid "Add-ons"
msgstr "Add-on"

#: includes/widgets/text-editor.php:191
msgid "10"
msgstr "10"

#: includes/widgets/text-editor.php:190
msgid "9"
msgstr "9"

#: includes/widgets/text-editor.php:189
msgid "8"
msgstr "8"

#: includes/widgets/text-editor.php:188
msgid "7"
msgstr "7"

#: includes/widgets/text-editor.php:187
msgid "6"
msgstr "6"

#: includes/widgets/text-editor.php:186
msgid "5"
msgstr "5"

#: includes/widgets/text-editor.php:185
msgid "4"
msgstr "4"

#: includes/widgets/text-editor.php:184
msgid "3"
msgstr "3"

#: includes/widgets/text-editor.php:183
msgid "2"
msgstr "2"

#: includes/widgets/text-editor.php:182
msgid "1"
msgstr "1"

#: includes/widgets/icon-box.php:359 includes/widgets/image-box.php:324
msgid "Content Spacing"
msgstr "Spaziatura del contenuto"

#: includes/widgets/common-base.php:1140
msgid "Explore additional Premium Shape packs and use them in your site."
msgstr "Esplora altri pacchetti Premium Shape e usali nel tuo sito."

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:23
msgid "Enjoy Creative Freedom with Custom Code"
msgstr "Libera la tua creatività con il codice personalizzato"

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/settings/tools.php:344
msgid "It is strongly recommended to %1$sbackup the database%2$s before using replacing URLs."
msgstr "Ti suggeriamo fortemente di effettuare il %1$sbackup del database%2$s prima di utilizzare la sostituzione degli URL."

#: modules/apps/admin-apps-page.php:35
msgid "Please note that certain tools and services on this page are developed by third-party companies and are not part of Elementor's suite of products or support. Before using them, we recommend independently evaluating them. Additionally, when clicking on their action buttons, you may be redirected to an external website."
msgstr "Nota bene: alcuni strumenti e servizi presenti in questa pagina sono sviluppati da aziende terze e non fanno parte della suite di prodotti o del supporto di Elementor. Prima di utilizzarli, ti consigliamo di valutarli in modo indipendente. Inoltre, quando fai clic sui loro pulsanti di azione, potresti essere reindirizzato a un sito web esterno."

#: includes/controls/base-units.php:138
msgid "Custom unit"
msgstr "Unità personalizzate"

#: core/role-manager/role-manager.php:215
msgid "Giving broad access to edit the HTML widget can pose a security risk to your website because it enables users to run malicious scripts, etc."
msgstr "Dare ampio accesso alla modifica del widget HTML può rappresentare un rischio per la sicurezza del tuo sito web, in quanto consente agli utenti di eseguire script dannosi, ecc."

#: core/role-manager/role-manager.php:213
msgid "Enable the option to use the HTML widget"
msgstr "Abilita l'opzione di utilizzo del widget HTML"

#: includes/editor-templates/panel-elements.php:33
msgid "Access all Pro widgets."
msgstr "Accedi a tutti i widget Pro. "

#: includes/editor-templates/navigator.php:16
msgid "Access all Pro widgets"
msgstr "Accesso a tutti i widget Pro"

#: core/utils/hints.php:167 includes/controls/notice.php:83
msgid "Don’t show again."
msgstr "Non mostrare di nuovo."

#: includes/managers/controls.php:1301 assets/js/editor.js:53658
msgid "Scrolling Effects"
msgstr "Effetti di scorrimento"

#: includes/managers/controls.php:1319
#: modules/floating-buttons/base/widget-floating-bars-base.php:1453
#: assets/js/editor.js:53688
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:144
msgid "Sticky"
msgstr "In evidenza"

#: includes/managers/controls.php:1310 assets/js/editor.js:53673
msgid "Mouse Effects"
msgstr "Effetti del mouse"

#: core/admin/admin-notices.php:661 includes/controls/gallery.php:123
#: includes/controls/media.php:319 includes/widgets/heading.php:473
msgid "Install Plugin"
msgstr "Installa il plugin"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:35
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:48
msgid "* Requires an Advanced subscription or higher"
msgstr "* Richiede un abbonamento Avanzato o superiore"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:35
msgid "Collect lead submissions directly within your WordPress Admin to manage, analyze and perform bulk actions on the submitted lead*"
msgstr "Raccogli gli invii di lead direttamente all'interno dell'amministrazione di WordPress per gestire, analizzare ed eseguire azioni di massa sui lead inviati*"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:34
msgid "Integrate your favorite marketing software*"
msgstr "Integra il tuo software di marketing preferito*"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:33
msgid "Use any field to collect the information you need"
msgstr "Utilizza qualsiasi campo per raccogliere le informazioni necessarie"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:32
msgid "Create single or multi-step forms to engage and convert visitors"
msgstr "Crea moduli singoli o a più fasi per coinvolgere e convertire i visitatori"

#: includes/widgets/video.php:130
msgid "Get the Video Playlist widget and grow your toolbox with Elementor Pro."
msgstr "Ottieni il widget Video Playlist e fai crescere la tua cassetta degli attrezzi con Elementor Pro."

#: includes/widgets/testimonial.php:197
msgid "Designer"
msgstr "Designer"

#: includes/widgets/testimonial.php:182
msgid "John Doe"
msgstr "John Doe"

#: includes/widgets/button.php:104
msgid "Convert visitors into customers"
msgstr "Converti i visitatori in clienti"

#: includes/managers/controls.php:1270 assets/js/editor.js:53643
msgid "Display Conditions"
msgstr "Visualizza le condizioni"

#: includes/editor-templates/templates.php:457
msgid "Generate Variations"
msgstr "Genera varianti"

#: includes/controls/groups/background.php:162
msgid "Set locations and angle for each breakpoint to ensure the gradient adapts to different screen sizes."
msgstr "Imposta i colori, la posizione e l'angolazione per ciascun punto di interruzione per garantire che il gradiente si adatti alle diverse dimensioni dello schermo."

#: includes/template-library/sources/local.php:817
msgid "Invalid template type or template does not exist."
msgstr "Tipo di template non valido o template non esistente."

#: includes/template-library/manager.php:409
#: includes/template-library/manager.php:552
#: includes/template-library/sources/local.php:822
msgid "You do not have permission to access this template."
msgstr "Non hai i permessi per accedere a questo template."

#: includes/template-library/sources/local.php:826
msgid "You do not have permission to export this template."
msgstr "Non hai i permessi per esportare questo template."

#: includes/widgets/video.php:129
msgid "Grab your visitors' attention"
msgstr "Attira l'attenzione di chi visita il tuo sito"

#: includes/widgets/button.php:105
msgid "Get the Call to Action widget and grow your toolbox with Elementor Pro."
msgstr "Ottieni il widget Invito all'azione (CTA) e fai crescere la tua cassetta degli attrezzi con Elementor Pro."

#: core/utils/hints.php:459
msgid "install Now"
msgstr "installalo ORA"

#: core/role-manager/role-manager.php:197
msgid "Giving broad access to upload JSON files can pose a security risk to your website because such files may contain malicious scripts, etc."
msgstr "Dare ampio accesso al caricamento di file JSON può rappresentare un rischio per la sicurezza del tuo sito web, perché tali file possono contenere script dannosi, ecc."

#: core/role-manager/role-manager.php:197
#: core/role-manager/role-manager.php:215
msgid "Heads up"
msgstr "Attenzione"

#: core/role-manager/role-manager.php:195
msgid "Enable the option to upload JSON files"
msgstr "Abilita l'opzione di caricamento dei file JSON"

#: core/files/uploads-manager.php:290
msgid "Invalid file name."
msgstr "Nome del file non valido."

#: includes/editor-templates/templates.php:176
#: assets/js/element-manager-admin.js:663
#: assets/js/element-manager-admin.js:732
msgid "Usage"
msgstr "Utilizzo"

#: modules/element-manager/ajax.php:154
msgid "Unexpected elements data."
msgstr "Dati di elementi inaspettati."

#: modules/element-manager/ajax.php:148
msgid "No elements to save."
msgstr "Nessun elemento da salvare."

#: modules/element-manager/ajax.php:127
msgid "WordPress Widgets"
msgstr "Widget WordPress"

#: modules/element-manager/ajax.php:117
msgid "Invalid nonce."
msgstr "Nonce non valido."

#: modules/element-manager/ajax.php:112
msgid "You do not have permission to edit these settings."
msgstr "Non hai i permessi per modificare queste impostazioni."

#: includes/widgets/image-gallery.php:108 includes/widgets/testimonial.php:114
msgid "Use interesting masonry layouts and other overlay features with Elementor's Pro Gallery widget."
msgstr "Usa interessanti layout masonry e altre funzionalità di overlay con il widget Galleria Pro di Elementor."

#: includes/widgets/image-carousel.php:128
msgid "Gain complete freedom to design every slide with Elementor\"s Pro Carousel."
msgstr "Con Carosello Pro di Elementor puoi progettare ogni singola slide con la massima libertà."

#: includes/template-library/sources/local.php:238
msgid "Parent Template:"
msgstr "Template genitore:"

#: includes/template-library/sources/local.php:237
msgid "No Templates found in Trash"
msgstr "Nessun template trovato nel cestino"

#: includes/template-library/sources/local.php:236
msgid "No Templates found"
msgstr "Nessun template trovato"

#: includes/template-library/sources/local.php:235
msgid "Search Template"
msgstr "Cerca template"

#: includes/template-library/sources/local.php:234
msgid "View Template"
msgstr "Visualizza template"

#: includes/template-library/sources/local.php:233
msgid "All Templates"
msgstr "Tutti i template"

#: includes/template-library/sources/local.php:229
#: includes/template-library/sources/local.php:230
msgid "Add New Template"
msgstr "Aggiungi nuovo template"

#: includes/controls/groups/image-size.php:296 includes/controls/media.php:297
#: includes/widgets/testimonial.php:328
msgid "Image Resolution"
msgstr "Risoluzione immagine"

#: includes/controls/groups/flex-container.php:183
#: includes/controls/groups/flex-container.php:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:152
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:154
msgid "Wrap"
msgstr "Wrap"

#: core/experiments/manager.php:361
msgid "Container-based content will be hidden from your site and may not be recoverable in all cases."
msgstr "I contenuti basati su contenitori saranno nascosti dal tuo sito e potrebbero non essere recuperabili in tutti i casi."

#. translators: %s: Widget title.
#: modules/promotions/widgets/pro-widget-promotion.php:57
msgid "This result includes the Elementor Pro %s widget. Upgrade now to unlock it and grow your web creation toolkit."
msgstr "Questo risultato include il widget Elementor Pro %s. Passa subito al piano Pro per sbloccarlo e ampliare il tuo kit di strumenti per la creazione di siti web."

#: includes/controls/groups/flex-container.php:187
msgid "No Wrap"
msgstr "Nessun wrap"

#: core/common/modules/finder/categories/settings.php:79
#: modules/element-manager/admin-menu-app.php:22
#: modules/element-manager/admin-menu-app.php:26
#: modules/element-manager/admin-menu-app.php:35
msgid "Element Manager"
msgstr "Element Manager"

#: includes/widgets/heading.php:159
msgid "Create captivating headings that rotate with the Animated Headline Widget."
msgstr "Crea intestazioni accattivanti che ruotano con il widget Titolo animato."

#: includes/editor-templates/templates.php:539 assets/js/ai-admin.js:1031
#: assets/js/ai-admin.js:1078 assets/js/ai-admin.js:2488
#: assets/js/ai-admin.js:3215 assets/js/ai-gutenberg.js:1169
#: assets/js/ai-gutenberg.js:1216 assets/js/ai-gutenberg.js:2626
#: assets/js/ai-gutenberg.js:3353 assets/js/ai-layout.js:729
#: assets/js/ai-layout.js:776 assets/js/ai-media-library.js:1031
#: assets/js/ai-media-library.js:1078 assets/js/ai-media-library.js:2488
#: assets/js/ai-media-library.js:3215 assets/js/ai-unify-product-images.js:1031
#: assets/js/ai-unify-product-images.js:1078
#: assets/js/ai-unify-product-images.js:2488
#: assets/js/ai-unify-product-images.js:3215 assets/js/ai.js:1814
#: assets/js/ai.js:1861 assets/js/ai.js:3271 assets/js/ai.js:3998
#: assets/js/editor.js:8244 assets/js/editor.js:8246 assets/js/editor.js:9823
#: assets/js/editor.js:9824
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4265
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:467
msgid "Upgrade now"
msgstr "Aggiorna ora"

#. translators: %s: Recommended PHP version.
#: modules/system-info/reporters/server.php:131
msgid "We recommend using PHP version %s or higher."
msgstr "Si consiglia di utilizzare PHP versione %s o superiore."

#: modules/page-templates/module.php:158
msgid "Elementor Full Width"
msgstr "Elementor Larghezza piena"

#: modules/page-templates/module.php:157
msgid "Elementor Canvas"
msgstr "Elementor Canvas"

#: modules/nested-accordion/widgets/nested-accordion.php:320
msgid "Let Google know that this section contains an FAQ. Make sure to only use it only once per page"
msgstr "Segnala a Google che questa sezione contiene una FAQ. Assicurati di usarla solo una volta per pagina."

#: modules/image-loading-optimization/module.php:240
msgid "An image should not be lazy-loaded and marked as high priority at the same time."
msgstr "Un'immagine non dovrebbe essere caricata in lazy load e contrassegnata allo stesso tempo come ad alta priorità."

#: includes/settings/settings.php:446
msgid "Optimized Gutenberg Loading"
msgstr "Caricamento di Gutenberg ottimizzato"

#: includes/widgets/video.php:313
msgid "VideoPress URL"
msgstr "URL di VideoPress"

#: includes/widgets/video.php:162
msgid "VideoPress"
msgstr "VideoPress"

#: includes/widgets/star-rating.php:129
msgid "You are currently editing a Star Rating widget in its old version. Drag a new Rating widget onto your page to use a newer version, providing better capabilities."
msgstr "Stai modificando un widget Stelle nella sua vecchia versione. Trascina un nuovo widget Stelle nella tua pagina per utilizzare una versione più recente, che offre funzionalità migliorate."

#. translators: 1: Rating value, 2: Rating scale.
#: includes/widgets/rating.php:310
msgid "Rated %1$s out of %2$s"
msgstr "Valutazione %1$s su %2$s"

#: includes/settings/settings.php:429
msgid "Optimized Image Loading"
msgstr "Caricamento immagine ottimizzato"

#: includes/settings/tools.php:165
msgid "An error occurred, the selected version is invalid. Try selecting different version."
msgstr "Si è verificato un errore, la versione selezionata non è valida. Prova a selezionare una versione diversa."

#: includes/controls/groups/typography.php:198
msgid "Line Height"
msgstr "Altezza della linea"

#: includes/controls/groups/image-size.php:380
#: modules/atomic-widgets/image/image-sizes.php:38
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:140
msgid "Full"
msgstr "Pieno"

#: includes/controls/groups/image-size.php:301
msgid "Image Dimension"
msgstr "Dimensioni dell'immagine"

#: includes/controls/groups/grid-container.php:131
msgid "Justify Items"
msgstr "Giustifica elementi"

#: includes/controls/groups/flex-item.php:173
msgid "Flex Shrink"
msgstr "Restringimento flessibile"

#: includes/controls/groups/flex-item.php:159
msgid "Flex Grow"
msgstr "Crescita flessibile"

#: includes/controls/groups/flex-item.php:138
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:169
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:172
msgid "Shrink"
msgstr "Restringi"

#: includes/controls/groups/flex-item.php:134
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:168
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:171
msgid "Grow"
msgstr "Crescere"

#: includes/controls/groups/flex-item.php:76
#: includes/controls/groups/flex-item.php:109
msgid "This control will affect contained elements only."
msgstr "Questo controllo avrà effetto solo sugli elementi contenuti."

#: includes/controls/groups/flex-item.php:20
msgid "Flex Basis"
msgstr "Base Flex"

#: includes/controls/groups/flex-container.php:204
#: includes/controls/groups/grid-container.php:226
msgid "Align Content"
msgstr "Allineamento contenuto"

#: includes/controls/groups/flex-container.php:195
msgid "Items within the container can stay in a single line (No wrap), or break into multiple lines (Wrap)."
msgstr "Gli elementi all'interno del contenitore possono rimanere su un'unica riga (No wrap) o dividersi in più righe (Wrap)."

#: includes/controls/groups/flex-container.php:128
#: includes/controls/groups/grid-container.php:159
msgid "Align Items"
msgstr "Allinea elementi"

#: includes/controls/groups/flex-container.php:91
#: includes/controls/groups/grid-container.php:186
msgid "Justify Content"
msgstr "Giustifica il contenuto"

#: includes/controls/groups/flex-container.php:45
msgid "Column - reversed"
msgstr "Colonna - invertita"

#: includes/controls/groups/flex-container.php:41
msgid "Row - reversed"
msgstr "Riga - invertita"

#: includes/controls/groups/flex-container.php:37
msgid "Column - vertical"
msgstr "Colonna - verticale"

#: includes/controls/groups/flex-container.php:33
msgid "Row - horizontal"
msgstr "Riga - orizzontale"

#: includes/controls/groups/css-filter.php:162
msgid "CSS Filters"
msgstr "Filtri CSS"

#: includes/controls/groups/box-shadow.php:61
#: includes/controls/groups/box-shadow.php:96
msgid "Box Shadow"
msgstr "Ombra riquadro"

#: includes/controls/groups/border.php:69
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:230
msgid "Groove"
msgstr "Scanalatura"

#: includes/controls/groups/border.php:60
msgid "Border Type"
msgstr "Tipo di bordo"

#: includes/controls/groups/background.php:607
msgid "Background Fallback"
msgstr "Sfondo di fallback"

#: includes/controls/groups/background.php:478
msgid "Display Size"
msgstr "Dimensioni di visualizzazione"

#: includes/controls/groups/background.php:301
msgid "Background Image"
msgstr "Immagine di sfondo"

#: includes/controls/groups/background.php:245
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:56
msgid "Angle"
msgstr "Angolo"

#: includes/controls/groups/background.php:234
msgid "Radial"
msgstr "Radiale"

#: includes/controls/groups/background.php:233
msgid "Linear"
msgstr "Lineare"

#: includes/controls/groups/background.php:201
msgid "Second Color"
msgstr "Colore secondario"

#: includes/controls/groups/background.php:154
msgid "Background Type"
msgstr "Tipo di sfondo"

#: includes/controls/groups/background.php:95
msgid "Classic"
msgstr "Classico"

#: includes/controls/groups/text-shadow.php:61
#: includes/controls/groups/text-shadow.php:85
msgid "Text Shadow"
msgstr "Ombra del Testo"

#: includes/controls/groups/flex-item.php:113
msgid "Custom Order"
msgstr "Ordine Personalizzato"

#: includes/controls/groups/typography.php:220
msgid "Letter Spacing"
msgstr "Spaziatura tra caratteri"

#: includes/controls/groups/flex-item.php:51
msgid "Align Self"
msgstr "Allineamento"

#: app/modules/onboarding/module.php:140
msgid "You do not have permission to perform this action."
msgstr "Non hai i permessi per eseguire questa azione."

#: modules/nested-tabs/widgets/nested-tabs.php:1170
#: modules/nested-tabs/widgets/nested-tabs.php:1235
msgid "Tabs. Open items with Enter or Space, close with Escape and navigate using the Arrow keys."
msgstr "Schede. Apri gli elementi con Invio o Spazio, chiudili con Esc e naviga con i tasti freccia."

#. translators: %s: Post type (e.g. Page, Post, etc.)
#: core/document-types/page-base.php:186
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:17
msgid "View %s"
msgstr "Visualizza %s"

#: modules/site-navigation/module.php:68
msgid "Pages Panel"
msgstr "Pannello pagine"

#: modules/nested-tabs/widgets/nested-tabs.php:199
#: modules/nested-tabs/widgets/nested-tabs.php:869
msgid "Below"
msgstr "Sotto"

#: modules/nested-tabs/widgets/nested-tabs.php:195
#: modules/nested-tabs/widgets/nested-tabs.php:861
msgid "Above"
msgstr "Sopra"

#: includes/editor-templates/templates.php:334
#: includes/editor-templates/templates.php:367
#: includes/editor-templates/templates.php:424 assets/js/editor.js:8755
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:17
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:73
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:51
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:19
msgid "Rename"
msgstr "Rinomina"

#: modules/nested-accordion/widgets/nested-accordion.php:401
msgid "Space between Items"
msgstr "Spazio tra gli elementi"

#: modules/nested-accordion/widgets/nested-accordion.php:357
msgid "Multiple"
msgstr "Multipla"

#: modules/nested-accordion/widgets/nested-accordion.php:356
msgid "One"
msgstr "Una"

#: modules/nested-accordion/widgets/nested-accordion.php:353
msgid "Max Items Expanded"
msgstr "Numero massimo di elementi espansi"

#: modules/nested-accordion/widgets/nested-accordion.php:343
msgid "All collapsed"
msgstr "Tutte compresse"

#: modules/nested-accordion/widgets/nested-accordion.php:342
msgid "First expanded"
msgstr "Prima espansa"

#: modules/nested-accordion/widgets/nested-accordion.php:339
msgid "Default State"
msgstr "Stato predefinito"

#: modules/nested-accordion/widgets/nested-accordion.php:332
msgid "Interactions"
msgstr "Interazioni"

#: modules/nested-accordion/widgets/nested-accordion.php:252
msgid "Collapse"
msgstr "Riduci"

#: modules/nested-accordion/widgets/nested-accordion.php:238
#: assets/js/ai-admin.js:9377 assets/js/ai-gutenberg.js:9595
#: assets/js/ai-layout.js:4868 assets/js/ai-media-library.js:9377
#: assets/js/ai-unify-product-images.js:9377 assets/js/ai.js:10838
msgid "Expand"
msgstr "Espandi"

#: modules/nested-accordion/widgets/nested-accordion.php:168
msgid "Item Position"
msgstr "Posizione elemento"

#: includes/widgets/video.php:941
msgid "Note: These controls have been deprecated and are only visible if they were previously in use. The video’s width and position are now set based on its aspect ratio."
msgstr "Nota: questi controlli sono stati deprecati e sono visibili solo se sono stati utilizzati in precedenza. La larghezza e la posizione del video sono ora impostate in base alle sue proporzioni."

#: includes/widgets/video.php:256
msgid "Choose Video File"
msgstr "Scegli file video"

#. translators: 1: Slide count, 2: Total slides count.
#: includes/widgets/image-carousel.php:980
msgid "%1$s of %2$s"
msgstr "%1$s di %2$s"

#: includes/widgets/icon-box.php:229 includes/widgets/image-box.php:204
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1456
msgid "Box"
msgstr "Riquadro"

#. translators: 1: Link open tag, 2: Link open tag, 3: Link close tag.
#: core/kits/documents/tabs/settings-site-identity.php:60
msgid "Changes will be reflected only after %1$s saving %3$s and %2$s reloading %3$s preview."
msgstr "Le modifiche si rifletteranno solo dopo %1$s il salvataggio %3$s e %2$s la ricarica %3$s dell'anteprima."

#: core/admin/admin.php:628
msgid "Build Smart with AI"
msgstr "Costruisci in modo intelligente con l'AI"

#: modules/apps/admin-apps-page.php:27
msgid "Learn more about this page."
msgstr "Approfondisci su questa pagina."

#: modules/apps/admin-apps-page.php:26
msgid "Boost your web-creation process with add-ons, plugins, and more tools specially selected to unleash your creativity, increase productivity, and enhance your Elementor-powered website."
msgstr "Potenzia il tuo processo di creazione web con add-on, plugin e altri strumenti appositamente selezionati per liberare la tua creatività, aumentare la produttività e migliorare il tuo sito web basato su Elementor."

#: modules/apps/admin-pointer.php:30
msgid "Discover our collection of plugins and add-ons carefully selected to enhance your Elementor website and unleash your creativity."
msgstr "Scopri la nostra raccolta di plugin e add-on accuratamente selezionati per migliorare il tuo sito web Elementor e aumentare la tua creatività."

#: modules/apps/admin-apps-page.php:25
msgid "Popular Add-ons, New Possibilities."
msgstr "Add-on popolari, nuove possibilità."

#: modules/nested-accordion/widgets/nested-accordion.php:66
msgid "item #%s"
msgstr "elemento #%s"

#: modules/floating-buttons/base/widget-floating-bars-base.php:370
#: modules/nested-accordion/widgets/nested-accordion.php:151
msgid "Item #1"
msgstr "Elemento #1"

#: modules/floating-buttons/base/widget-floating-bars-base.php:373
#: modules/nested-accordion/widgets/nested-accordion.php:154
msgid "Item #2"
msgstr "Elemento #2"

#: modules/floating-buttons/base/widget-floating-bars-base.php:376
#: modules/nested-accordion/widgets/nested-accordion.php:157
msgid "Item #3"
msgstr "Elemento #3"

#: modules/floating-buttons/base/widget-floating-bars-base.php:340
#: modules/nested-accordion/widgets/nested-accordion.php:117
#: modules/nested-accordion/widgets/nested-accordion.php:118
msgid "Item Title"
msgstr "Titolo elemento"

#: includes/frontend.php:1393
msgid "Go to slide"
msgstr "Vai alla diapositiva"

#: includes/frontend.php:1392
msgid "This is the last slide"
msgstr "Questa è l'ultima diapositiva"

#: includes/frontend.php:1391
msgid "This is the first slide"
msgstr "Questa é la prima diapositiva"

#: includes/frontend.php:1390
msgid "Next slide"
msgstr "Prossima diapositiva"

#: includes/frontend.php:1389
msgid "Previous slide"
msgstr "Diapositiva precedente"

#: includes/controls/gallery.php:84 assets/js/editor.js:14681
msgid "Clear gallery"
msgstr "Cancella galleria"

#: includes/editor-templates/navigator.php:81
msgid "Show/hide Element"
msgstr "Mostra/nascondi elemento"

#: includes/editor-templates/navigator.php:58
msgid "Resize structure"
msgstr "Ridimensiona struttura"

#: core/document-types/page-base.php:257
msgid "Allow Comments"
msgstr "Permetti di commentare"

#: core/document-types/page-base.php:245
#: includes/controls/groups/flex-item.php:80
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:174
msgid "Order"
msgstr "Ordinamento"

#: includes/editor-templates/hotkeys.php:91
msgid "Panels"
msgstr "Pannelli"

#: modules/nested-tabs/widgets/nested-tabs.php:377
msgid "Horizontal Scroll"
msgstr "Scroll orizzontale"

#: includes/widgets/image-box.php:423 includes/widgets/image.php:398
msgid "Object Position"
msgstr "Posizione oggetto"

#: includes/widgets/icon.php:326
msgid "Fit to Size"
msgstr "Adatta alle dimensioni"

#: includes/editor-templates/navigator.php:68
msgid "Show/hide inner elements"
msgstr "Mostra/nascondi elementi interni"

#: includes/controls/groups/background.php:99 assets/js/ai-admin.js:11307
#: assets/js/ai-gutenberg.js:11525 assets/js/ai-media-library.js:11307
#: assets/js/ai-unify-product-images.js:11307 assets/js/ai.js:12768
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:144
msgid "Gradient"
msgstr "Gradiente"

#: modules/nested-tabs/widgets/nested-tabs.php:379
msgid "Note: Scroll tabs if they don’t fit into their parent container."
msgstr "Nota: scorrere le schede se non si adattano al loro contenitore genitore."

#: includes/widgets/toggle.php:157
msgid "You are currently editing a Toggle widget in its old version. Drag a new Accordion widget onto your page to use a newer version, providing nested capabilities."
msgstr "Stai modificando un widget Attiva/disattiva nella sua vecchia versione. Trascina un nuovo widget Accordion nella tua pagina per utilizzare una versione più recente, che offre funzionalità di annidamento."

#: includes/widgets/accordion.php:154
msgid "You are currently editing an Accordion Widget in its old version. Any new Accordion widget dragged into the canvas will be the new Accordion widget, with the improved Nested capabilities."
msgstr "Stai modificando un widget Accordion nella sua vecchia versione. Ogni nuovo widget Accordion trascinato nell'area di lavoro sarà il nuovo widget Accordion, con le funzionalità migliorate di annidamento."

#: includes/template-library/sources/local.php:515
msgid "Invalid template type."
msgstr "Tipo di template non valido."

#: core/admin/admin.php:347
msgid "Get Elementor Pro"
msgstr "Ottieni Elementor Pro"

#. translators: %s: Document title.
#: core/editor/loader/v1/templates/editor-body-v1-view.php:27
#: core/editor/loader/v2/templates/editor-body-v2-view.php:27
#: includes/editor-templates/editor-wrapper.php:30
#: assets/js/packages/editor-documents/editor-documents.js:2
#: assets/js/packages/editor-documents/editor-documents.strings.js:2
msgid "Edit \"%s\" with Elementor"
msgstr "Modifica \"%s\" con Elementor"

#: includes/editor-templates/navigator.php:32 assets/js/editor.js:35705
msgid "Expand all elements"
msgstr "Espandi tutti gli elementi"

#: includes/editor-templates/panel-elements.php:15
msgid "Globals"
msgstr "Globali"

#: includes/editor-templates/navigator.php:36
msgid "Close structure"
msgstr "Chiudi struttura"

#: includes/editor-templates/global.php:9
msgid "Select your structure"
msgstr "Seleziona la tua struttura"

#: includes/controls/gaps.php:58
#: includes/controls/groups/grid-container.php:118
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:48
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:180
msgid "Row"
msgstr "Riga"

#: includes/controls/groups/grid-container.php:71
msgid "Rows"
msgstr "Righe"

#: modules/floating-buttons/base/widget-contact-button-base.php:116
msgid "Top Bar"
msgstr "Barra superiore"

#: includes/elements/container.php:372
msgid "Container Layout"
msgstr "Layout del contenitore"

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/widgets/image-carousel.php:391
#: includes/widgets/image-gallery.php:204 includes/widgets/image.php:229
msgid "Manage your site’s lightbox settings in the %1$sLightbox panel%2$s."
msgstr "Gestisci le impostazioni delle lightbox del tuo sito nel %1$spannello Lightbox%2$s."

#: includes/editor-templates/global.php:52
msgid "Which layout would you like to use?"
msgstr "Quale layout desideri utilizzare?"

#: includes/editor-templates/global.php:60 includes/elements/container.php:377
#: modules/atomic-widgets/elements/flexbox/flexbox.php:23
#: modules/library/documents/flexbox.php:52 assets/js/editor.js:8486
msgid "Flexbox"
msgstr "Flexbox"

#: includes/editor-templates/global.php:68 includes/elements/container.php:96
#: includes/elements/container.php:104 includes/elements/container.php:378
#: assets/js/editor.js:33644 assets/js/editor.js:42731
msgid "Grid"
msgstr "Griglia"

#: includes/controls/groups/grid-container.php:31
msgid "Grid Outline"
msgstr "Contorno griglia"

#. translators: 1: Link open tag, 2: Link close tag.
#: core/document-types/page-base.php:100
msgid "Set a different selector for the title in the %1$sLayout panel%2$s."
msgstr "Imposta un selettore diverso per il titolo nel %1$spannello Layout%2$s."

#: includes/controls/base-units.php:130
msgid "Switch units"
msgstr "Cambia unità"

#: modules/ai/connect/ai.php:27 assets/js/ai-admin.js:654
#: assets/js/ai-admin.js:7763 assets/js/ai-gutenberg.js:792
#: assets/js/ai-gutenberg.js:7981 assets/js/ai-layout.js:486
#: assets/js/ai-layout.js:3254 assets/js/ai-media-library.js:654
#: assets/js/ai-media-library.js:7763 assets/js/ai-unify-product-images.js:654
#: assets/js/ai-unify-product-images.js:7763 assets/js/ai.js:1437
#: assets/js/ai.js:9224
msgid "AI"
msgstr "AI"

#: core/kits/documents/tabs/settings-layout.php:100
#: includes/controls/groups/flex-container.php:156
#: includes/controls/groups/grid-container.php:96
#: includes/elements/container.php:509
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:166
msgid "Gaps"
msgstr "Spaziature"

#: includes/controls/groups/grid-container.php:115
msgid "Auto Flow"
msgstr "Flusso automatico"

#: core/breakpoints/manager.php:329
msgid "Tablet Landscape"
msgstr "Tablet orizzontale"

#: core/breakpoints/manager.php:324
msgid "Tablet Portrait"
msgstr "Tablet verticale"

#: includes/elements/column.php:211 includes/widgets/icon-list.php:541
msgid "Horizontal Alignment"
msgstr "Allineamento orizzontale"

#: includes/widgets/alert.php:506 includes/widgets/alert.php:561
msgid "Dismiss this alert."
msgstr "Ignora questo avviso."

#: includes/widgets/icon-list.php:598
msgid "Adjust Vertical Position"
msgstr "Regola la posizione verticale"

#: core/admin/admin-notices.php:762 core/admin/admin.php:1036
msgid "Dismiss this notice."
msgstr "Ignora questa notifica."

#: core/kits/documents/tabs/theme-style-form-fields.php:188
msgid "Accent Color"
msgstr "Colore in risalto"

#. translators: %d: Number of rows.
#: includes/utils.php:254
msgid "%d database row affected."
msgid_plural "%d database rows affected."
msgstr[0] "%d riga del database interessata."
msgstr[1] "%d righe del database interessate."

#: core/experiments/manager.php:540
msgid "Deactivate All"
msgstr "Disattiva tutto"

#: core/experiments/manager.php:539
msgid "Activate All"
msgstr "Attiva tutto"

#: core/experiments/manager.php:516
msgid "Experiments and Features"
msgstr "Esperimenti e Caratteristiche"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: core/experiments/manager.php:522
msgid "Personalize your Elementor experience by controlling which features and experiments are active on your site. Help make Elementor better by %1$ssharing your experience and feedback with us%2$s."
msgstr "Personalizza la tua esperienza con Elementor controllando quali caratteristiche ed esperimenti sono attivi sul tuo sito. Contribuisci a migliorare Elementor %1$scondividendo la tua esperienza e il tuo feedback con noi%2$s."

#: modules/generator-tag/module.php:76
msgid "Generator Tag"
msgstr "Tag generatore"

#: modules/generator-tag/module.php:84
msgid "A generator tag is a meta element that indicates the attributes used to create a webpage. It is used for analytical purposes."
msgstr "Il tag generatore è un meta-elemento che indica gli attributi utilizzati per creare una pagina web. Viene utilizzato a scopo di analisi."

#: modules/atomic-opt-in/opt-in-page.php:90
#: modules/atomic-widgets/opt-in.php:41 assets/js/editor-v4-opt-in.js:344
#: assets/js/editor-v4-opt-in.js:502
msgid "Editor V4"
msgstr "Editor V4"

#: modules/nested-tabs/widgets/nested-tabs.php:713
msgid "Titles"
msgstr "Titoli"

#: includes/widgets/video.php:865
msgid "Shadow"
msgstr "Ombra"

#: modules/nested-accordion/widgets/nested-accordion.php:427
#: modules/nested-tabs/widgets/nested-tabs.php:469
msgid "Distance from content"
msgstr "Distanza dal contenuto"

#: modules/nested-tabs/widgets/nested-tabs.php:449
msgid "Gap between tabs"
msgstr "Spazio tra le schede"

#. translators: 1: Breakpoint label, 2: `>` character, 3: Breakpoint value.
#: modules/nested-tabs/widgets/nested-tabs.php:422
msgid "%1$s (%2$s %3$dpx)"
msgstr "%1$s (%2$s %3$dpx)"

#: modules/nested-tabs/widgets/nested-tabs.php:178
msgid "Tab #3"
msgstr "Scheda #3"

#: modules/nested-tabs/widgets/nested-tabs.php:80
msgid "Tab #%d"
msgstr "Scheda #%d"

#: modules/nested-tabs/widgets/nested-tabs.php:61
msgid "Tab #%s"
msgstr "Scheda #%s"

#: includes/settings/settings.php:365
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:249
msgid "Google Fonts"
msgstr "Google Fonts"

#: modules/nested-tabs/widgets/nested-tabs.php:342
msgid "Align Title"
msgstr "Allineamento titolo"

#: includes/settings/settings.php:374
msgid "Disable this option if you want to prevent Google Fonts from being loaded. This setting is recommended when loading fonts from a different source (plugin, theme or %1$scustom fonts%2$s)."
msgstr "Disattiva questa opzione se desideri impedire il caricamento dei Google Fonts. Questa impostazione è consigliata quando si caricano i font da una sorgente diversa (plugin, tema o %1$sfont personalizzati%2$s)."

#: modules/nested-elements/module.php:17
msgid "Nested Elements"
msgstr "Elementi annidati"

#: includes/widgets/tabs.php:153
msgid "You are currently editing a Tabs Widget in its old version. Any new tabs widget dragged into the canvas will be the new Tab widget, with the improved Nested capabilities."
msgstr "Stai modificando un widget Schede nella sua vecchia versione. Ogni nuovo widget schede trascinato nell'area di lavoro sarà il nuovo widget Schede, con le funzionalità migliorate di annidamento."

#: includes/widgets/video.php:599
msgid "Metadata"
msgstr "Metadati"

#: includes/widgets/video.php:596
msgid "Preload"
msgstr "Precaricamento"

#: modules/nested-elements/module.php:20
msgid "Create a rich user experience by layering widgets together inside \"Nested\" Tabs, etc. When turned on, we’ll automatically enable new nested features. Your old widgets won’t be affected."
msgstr "Crea una ricca esperienza utente stratificando i widget all'interno di schede \"annidate\", ecc. Se attivate, abiliteremo automaticamente le nuove funzionalità annidate. I tuoi vecchi widget non saranno interessati."

#: includes/widgets/video.php:605
msgid "Preload attribute lets you specify how the video should be loaded when the page loads."
msgstr "L'attributo Precaricamento (Preload) consente di specificare come il video deve essere caricato al momento del caricamento della pagina."

#: modules/nested-tabs/widgets/nested-tabs.php:434
msgid "Note: Choose at which breakpoint tabs will automatically switch to a vertical (“accordion”) layout."
msgstr "Nota: scegli a quale punto di interruzione le schede passeranno automaticamente a un layout verticale (“accordion”)."

#: includes/elements/container.php:1939
msgid "Note: Avoid applying transform properties on sticky containers. Doing so might cause unexpected results."
msgstr "Nota: si consiglia di non applicare le proprietà di trasformazione ai contenitori fissi. Ciò potrebbe causare risultati inaspettati."

#: includes/settings/settings.php:458
msgid "Lazy Load Background Images"
msgstr "Lazy Load immagini di sfondo"

#: core/admin/admin.php:1024 core/utils/hints.php:460
#: modules/apps/admin-apps-page.php:116 modules/apps/admin-apps-page.php:147
#: modules/home/<USER>/filter-plugins.php:82 assets/js/admin.js:795
#: assets/js/editor-v4-opt-in.js:355
msgid "Activate"
msgstr "Attiva"

#: includes/elements/container.php:578
msgid "(link)"
msgstr "(link)"

#: includes/controls/groups/typography.php:150
msgctxt "Typography Control"
msgid "(Black)"
msgstr "(Nero)"

#: includes/controls/groups/typography.php:149
msgctxt "Typography Control"
msgid "(Extra Bold)"
msgstr "(Grassetto accentuato)"

#: includes/controls/groups/typography.php:148
msgctxt "Typography Control"
msgid "(Bold)"
msgstr "(Grassetto)"

#: includes/controls/groups/typography.php:147
msgctxt "Typography Control"
msgid "(Semi Bold)"
msgstr "(Semigrassetto)"

#: includes/controls/groups/typography.php:146
msgctxt "Typography Control"
msgid "(Medium)"
msgstr "(Medio)"

#: includes/controls/groups/typography.php:145
msgctxt "Typography Control"
msgid "(Normal)"
msgstr "(Normale)"

#: includes/controls/groups/typography.php:144
msgctxt "Typography Control"
msgid "(Light)"
msgstr "(Chiaro)"

#: includes/controls/groups/typography.php:143
msgctxt "Typography Control"
msgid "(Extra Light)"
msgstr "(Chiarissimo)"

#: includes/controls/groups/typography.php:142
msgctxt "Typography Control"
msgid "(Thin)"
msgstr "(Sottile)"

#: core/experiments/manager.php:612
msgid "Requires"
msgstr "Richiede"

#: app/modules/import-export-customization/module.php:174
#: app/modules/import-export-customization/module.php:177
#: app/modules/import-export-customization/module.php:183
#: app/modules/import-export/module.php:176
#: app/modules/import-export/module.php:179
#: app/modules/import-export/module.php:185
msgid "imported kit"
msgstr "kit importato"

#: app/modules/import-export-customization/module.php:182
#: app/modules/import-export/module.php:184
msgid "Remove all the content and site settings that came with \"%1$s\" on %2$s.%3$s Your original site settings will be restored."
msgstr "Rimuovi tutti i contenuti e le impostazioni del sito che sono stati forniti con \"%1$s\" su %2$s.%3$s Le impostazioni originali del tuo sito saranno ripristinate."

#: app/modules/import-export-customization/module.php:173
#: app/modules/import-export/module.php:175
msgid "Remove all the content and site settings that came with \"%1$s\" on %2$s %3$s and revert to the site setting that came with \"%4$s\" on %5$s."
msgstr "Rimuovi tutti i contenuti e le impostazioni del sito che sono stati forniti con \"%1$s\" su %2$s %3$s e ripristina le impostazioni del sito che sono state fornite con \"%4$s\" su %5$s."

#: includes/widgets/alert.php:394
#: modules/floating-buttons/base/widget-contact-button-base.php:2936
msgid "Horizontal Position"
msgstr "Posizione orizzontale"

#: includes/widgets/alert.php:376
#: modules/floating-buttons/base/widget-contact-button-base.php:2990
#: modules/floating-buttons/base/widget-floating-bars-base.php:1433
msgid "Vertical Position"
msgstr "Posizione verticale"

#: core/settings/editor-preferences/model.php:209
msgid "All Posts"
msgstr "Tutti gli articoli"

#: core/settings/editor-preferences/model.php:208
msgid "This Post"
msgstr "Questo articolo"

#: core/settings/editor-preferences/model.php:210
msgid "WP Dashboard"
msgstr "Bacheca WP"

#. Translators: %s is the current item index.
#: modules/nested-accordion/widgets/nested-accordion.php:85
#: modules/nested-elements/base/widget-nested-base.php:45
#: assets/js/editor.js:25265
msgid "Item #%d"
msgstr "Elemento #%d"

#: core/common/modules/finder/categories/settings.php:64
#: core/experiments/manager.php:313 core/experiments/manager.php:370
#: includes/settings/settings.php:400 includes/settings/settings.php:403
#: modules/element-cache/module.php:47
msgid "Performance"
msgstr "Prestazioni"

#: core/admin/admin-notices.php:407
msgid "Try it out"
msgstr "Provalo"

#: includes/widgets/image-carousel.php:303
msgid "Next Arrow Icon"
msgstr "Icona della freccia successiva"

#: includes/widgets/image-carousel.php:248
msgid "Previous Arrow Icon"
msgstr "Icona della freccia precedente"

#: includes/editor-templates/panel.php:57
msgid "Any time you can change the settings in %1$sUser Preferences%2$s"
msgstr "In qualsiasi momento puoi modificare le impostazioni in %1$sPreferenze utente%2$s"

#: core/kits/documents/tabs/settings-layout.php:88
msgid "Container Padding"
msgstr "Spaziatura interna del contenitore"

#: core/kits/documents/tabs/settings-layout.php:91
msgid "Sets the default space inside the container (Default is 10px)"
msgstr "Imposta la spaziatura predefinita all'interno del contenitore (Predefinito è 10px)"

#: core/admin/admin-notices.php:403
msgid "Improve your site’s performance score."
msgstr "Migliora il punteggio delle prestazioni del tuo sito."

#: includes/editor-templates/panel.php:53
msgid "Now you can choose where you want to go on the site from the following options"
msgstr "A questo punto puoi scegliere in quale punto del sito desideri andare tra le seguenti opzioni"

#: core/settings/editor-preferences/model.php:204
msgid "Exit to"
msgstr "Esci verso"

#: core/admin/admin-notices.php:404
msgid "With our experimental speed boosting features you can go faster than ever before. Look for the Performance label on our Experiments page and activate those experiments to improve your site loading speed."
msgstr "Con le nostre caratteristiche sperimentali di aumento della velocità potrai andare più veloce che mai. Cerca l'etichetta Prestazioni nella nostra pagina Esperimenti e attiva questi esperimenti per migliorare la velocità di caricamento del tuo sito."

#: includes/elements/container.php:599
msgid "Don’t add links to elements nested in this container - it will break the layout."
msgstr "Non aggiungere collegamenti a elementi annidati in questo contenitore, per non interrompere il layout."

#: includes/widgets/alert.php:161 includes/widgets/alert.php:342
msgid "Dismiss Icon"
msgstr "Icona \"Ignora\""

#: app/modules/onboarding/module.php:158
msgid "There was a problem setting your site name."
msgstr "Si è verificato un problema durante l'impostazione del nome del tuo sito."

#: core/editor/notice-bar.php:45 assets/js/element-manager-admin.js:725
msgid "Unleash the full power of Elementor's features and web creation tools."
msgstr "Libera il pieno potenziale delle funzionalità di Elementor e degli strumenti di creazione web."

#: core/editor/notice-bar.php:41 core/editor/promotion.php:34
#: includes/editor-templates/navigator.php:17
#: includes/editor-templates/panel-elements.php:29
#: includes/editor-templates/panel-elements.php:34
#: includes/editor-templates/panel-elements.php:103
#: includes/managers/controls.php:1161 includes/widgets/button.php:107
#: includes/widgets/heading.php:161 includes/widgets/image-carousel.php:130
#: includes/widgets/image-gallery.php:110 includes/widgets/testimonial.php:116
#: includes/widgets/video.php:132 modules/admin-top-bar/module.php:79
#: modules/checklist/steps/setup-header.php:93
#: modules/element-manager/ajax.php:73 modules/element-manager/ajax.php:80
#: modules/element-manager/ajax.php:88
#: modules/promotions/admin-menu-items/base-promotion-item.php:32
#: modules/promotions/admin-menu-items/base-promotion-template.php:37
#: modules/promotions/admin-menu-items/popups-promotion-item.php:24
#: modules/promotions/promotion-data.php:48
#: modules/promotions/promotion-data.php:65
#: modules/promotions/promotion-data.php:82
#: modules/promotions/promotion-data.php:99
#: modules/promotions/promotion-data.php:116 assets/js/app-packages.js:917
#: assets/js/app-packages.js:4293 assets/js/app-packages.js:4569
#: assets/js/app.js:1157 assets/js/checklist.js:241 assets/js/editor.js:6185
#: assets/js/editor.js:10877 assets/js/editor.js:53648
#: assets/js/editor.js:53663 assets/js/editor.js:53678
#: assets/js/editor.js:53693
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:467
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1406
msgid "Upgrade Now"
msgstr "Aggiorna ora"

#: core/admin/admin.php:632 core/role-manager/role-manager.php:241
#: includes/editor-templates/panel-elements.php:40
#: includes/editor-templates/panel-elements.php:46
#: includes/editor-templates/panel-elements.php:65
#: includes/editor-templates/panel.php:317
#: includes/editor-templates/templates.php:513
#: includes/managers/controls.php:1152 includes/widgets/button.php:103
#: includes/widgets/heading.php:158 includes/widgets/image-carousel.php:127
#: includes/widgets/image-gallery.php:107 includes/widgets/testimonial.php:113
#: includes/widgets/video.php:128
#: modules/promotions/admin-menu-items/go-pro-promotion-item.php:30
#: modules/promotions/promotion-data.php:41
#: modules/promotions/promotion-data.php:58
#: modules/promotions/promotion-data.php:75
#: modules/promotions/promotion-data.php:92
#: modules/promotions/promotion-data.php:109 assets/js/ai-admin.js:1021
#: assets/js/ai-admin.js:2957 assets/js/ai-admin.js:3085
#: assets/js/ai-gutenberg.js:1159 assets/js/ai-gutenberg.js:3095
#: assets/js/ai-gutenberg.js:3223 assets/js/ai-layout.js:719
#: assets/js/ai-layout.js:998 assets/js/ai-media-library.js:1021
#: assets/js/ai-media-library.js:2957 assets/js/ai-media-library.js:3085
#: assets/js/ai-unify-product-images.js:1021
#: assets/js/ai-unify-product-images.js:2957
#: assets/js/ai-unify-product-images.js:3085 assets/js/ai.js:1804
#: assets/js/ai.js:3740 assets/js/ai.js:3868 assets/js/app-packages.js:4534
#: assets/js/e-react-promotions.js:196 assets/js/editor.js:4706
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:1470
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3260
#: assets/js/notes.js:148
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:719
#: assets/js/styleguide.js:196
msgid "Upgrade"
msgstr "Aggiorna"

#: includes/editor-templates/hotkeys.php:184 assets/js/notes.js:135
#: assets/js/notes.js:139 assets/js/notes.js:225
msgid "Notes"
msgstr "Notes"

#: modules/announcements/module.php:118
msgid "Let's do it"
msgstr "Facciamolo"

#: modules/container-converter/module.php:86
#: modules/container-converter/module.php:119
msgid "Convert"
msgstr "Converti"

#: includes/widgets/image-carousel.php:635
msgid "Pagination"
msgstr "Paginazione"

#: core/experiments/manager.php:342 includes/elements/container.php:72
#: includes/elements/container.php:356
#: modules/library/documents/container.php:52 assets/js/editor.js:8484
#: assets/js/editor.js:33644 assets/js/editor.js:39479
msgid "Container"
msgstr "Contenitore"

#: includes/elements/column.php:460 includes/elements/section.php:729
#: includes/widgets/heading.php:337
msgid "Exclusion"
msgstr "Esclusione"

#: includes/elements/column.php:459 includes/elements/section.php:728
#: includes/widgets/heading.php:336
msgid "Difference"
msgstr "Differenza"

#: includes/controls/groups/typography.php:153
msgid "Bold"
msgstr "Grassetto"

#: includes/controls/groups/background.php:727
#: includes/widgets/image-carousel.php:434
msgid "Lazyload"
msgstr "Lazyload"

#: core/kits/views/panel.php:36
msgid "Reorder"
msgstr "Riordina"

#: core/kits/documents/tabs/global-typography.php:160
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:227
msgid "System Fonts"
msgstr "Font di sistema"

#: core/common/modules/finder/categories/tools.php:68
msgid "Import Export"
msgstr "Importa Esporta"

#: modules/container-converter/module.php:88
#: modules/container-converter/module.php:121
msgid "Copies all of the selected sections and columns and pastes them in a container beneath the original."
msgstr "Copia tutte le sezioni e le colonne selezionate e le incolla in un contenitore sotto l'originale."

#: modules/container-converter/module.php:85
#: modules/container-converter/module.php:118
msgid "Convert to container"
msgstr "Converti in contenitore"

#: includes/widgets/video.php:1001
msgid "Play Video about"
msgstr "Riproduci video su"

#: includes/elements/container.php:493
msgid "To achieve full height Container use %s."
msgstr "Per raggiungere la piena altezza al Contenitore utilizzare %s."

#: includes/editor-templates/global.php:33 assets/js/editor.js:33503
msgid "Add New Container"
msgstr "Aggiungi nuovo contenitore"

#: core/kits/documents/tabs/global-colors.php:101
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:178
msgid "System Colors"
msgstr "Colori di sistema"

#: core/common/modules/event-tracker/personal-data.php:24
msgid "Elementor Event Tracker"
msgstr "Tracker di eventi Elementor"

#: elementor.php:78 elementor.php:102 assets/js/ai-admin.js:1060
#: assets/js/ai-gutenberg.js:1198 assets/js/ai-layout.js:758
#: assets/js/ai-media-library.js:1060 assets/js/ai-unify-product-images.js:1060
#: assets/js/ai.js:1843 assets/js/app.js:7569
msgid "Show me how"
msgstr "Mostrami come"

#: includes/elements/column.php:461 includes/elements/section.php:730
#: includes/widgets/heading.php:338
msgid "Hue"
msgstr "Tonalità"

#: app/modules/onboarding/module.php:265 app/modules/onboarding/module.php:350
msgid "There was a problem uploading your file."
msgstr "Si è verificato un problema durante il caricamento del file."

#: app/modules/onboarding/module.php:215
msgid "There was a problem setting your site logo."
msgstr "Si è verificato un problema durante l'impostazione del logo del tuo sito."

#: modules/library/documents/page.php:65
msgid "Add New Page Template"
msgstr "Aggiungi nuovo template di pagina"

#: includes/base/element-base.php:1201
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:127
msgid "Skew Y"
msgstr "Distorsione Y"

#: includes/base/element-base.php:1179
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:126
msgid "Skew X"
msgstr "Distorsione X"

#: core/utils/hints.php:462
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:275
msgid "Customize"
msgstr "Personalizza"

#: includes/base/element-base.php:1244 includes/base/element-base.php:1248
msgid "Flip Vertical"
msgstr "Capovolgi Verticale"

#: includes/base/element-base.php:1225 includes/base/element-base.php:1229
msgid "Flip Horizontal"
msgstr "Rifletti orizzontale"

#: includes/base/element-base.php:1167
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:115
#: assets/js/packages/editor-controls/editor-controls.strings.js:120
msgid "Skew"
msgstr "Inclinazione"

#: includes/base/element-base.php:1048
msgid "Offset Y"
msgstr "Offset Y"

#: includes/base/element-base.php:1022
msgid "Offset X"
msgstr "Offset X"

#: includes/base/element-base.php:987
msgid "Perspective"
msgstr "Prospettiva"

#: includes/base/element-base.php:964
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:132
msgid "Rotate Y"
msgstr "Ruota Y"

#: includes/base/element-base.php:941
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:131
msgid "Rotate X"
msgstr "Ruota X"

#: includes/base/element-base.php:924
msgid "3D Rotate"
msgstr "Rotazione 3D"

#: includes/base/element-base.php:861
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:111
#: assets/js/packages/editor-controls/editor-controls.strings.js:116
msgid "Transform"
msgstr "Trasformazione"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:19
msgid "Custom Code"
msgstr "Codice personalizzato"

#: includes/admin-templates/beta-tester.php:53 assets/js/ai-admin.js:6375
#: assets/js/ai-admin.js:15874 assets/js/ai-gutenberg.js:6593
#: assets/js/ai-gutenberg.js:16092 assets/js/ai-layout.js:2484
#: assets/js/ai-layout.js:5192 assets/js/ai-media-library.js:6375
#: assets/js/ai-media-library.js:15874
#: assets/js/ai-unify-product-images.js:6375
#: assets/js/ai-unify-product-images.js:15874 assets/js/ai.js:7734
#: assets/js/ai.js:7836 assets/js/ai.js:17335
msgid "Privacy Policy"
msgstr "Privacy Policy"

#: includes/admin-templates/beta-tester.php:48 assets/js/ai-admin.js:6371
#: assets/js/ai-admin.js:15870 assets/js/ai-gutenberg.js:6589
#: assets/js/ai-gutenberg.js:16088 assets/js/ai-layout.js:2480
#: assets/js/ai-layout.js:5188 assets/js/ai-media-library.js:6371
#: assets/js/ai-media-library.js:15870
#: assets/js/ai-unify-product-images.js:6371
#: assets/js/ai-unify-product-images.js:15870 assets/js/ai.js:7730
#: assets/js/ai.js:7832 assets/js/ai.js:17331
msgid "Terms of Service"
msgstr "Termini di servizio"

#. translators: 1. "Terms of service" link, 2. "Privacy policy" link
#: includes/admin-templates/beta-tester.php:44
msgid "By clicking Sign Up, you agree to Elementor's %1$s and %2$s"
msgstr "Facendo clic su Iscriviti, accetti la %1$s e %2$s di Elementor"

#: core/logger/log-reporter.php:25
msgid "Log"
msgstr "Registro"

#: includes/base/element-base.php:1143
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:129
msgid "Scale Y"
msgstr "Scala Y"

#: includes/base/element-base.php:1121
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:128
msgid "Scale X"
msgstr "Scala X"

#: includes/base/element-base.php:1088
msgid "Keep Proportions"
msgstr "Mantieni proporzioni"

#: modules/usage/usage-reporter.php:22
msgid "Elements Usage"
msgstr "Utilizzo degli elementi"

#: core/experiments/manager.php:545
msgid "Ongoing Experiments"
msgstr "Esperimenti in corso"

#: core/experiments/manager.php:502
msgid "Stable Features"
msgstr "Caratteristiche stabili"

#. translators: 1: Integration settings link open tag, 2: Create API key link
#. open tag, 3: Link close tag.
#: includes/widgets/google-maps.php:140
msgid "Set your Google Maps API Key in Elementor's %1$sIntegrations Settings%3$s page. Create your key %2$shere.%3$s"
msgstr "Imposta la chiave API di Google Maps nella pagina %1$sImpostazioni Integrazioni%3$s di Elementor. Crea la tua chiave %2$squi.%3$s"

#: core/editor/promotion.php:31 assets/js/e-react-promotions.js:196
#: assets/js/editor.js:4706 assets/js/notes.js:148 assets/js/styleguide.js:196
msgid "Connect & Activate"
msgstr "Connetti e attiva"

#: includes/controls/groups/text-stroke.php:85
msgid "Stroke Color"
msgstr "Colore del contorno"

#: includes/controls/groups/text-stroke.php:60
#: includes/controls/groups/text-stroke.php:111
msgid "Text Stroke"
msgstr "Contornato"

#: includes/base/element-base.php:1346
msgid "Y Anchor Point"
msgstr "Coordinata Y dell'ancora"

#: includes/base/element-base.php:1318
msgid "X Anchor Point"
msgstr "Coordinata X dell'ancora"

#: core/experiments/manager.php:312
msgid "Inline Font Icons"
msgstr "Icone dei font in linea"

#: core/experiments/experiments-reporter.php:21
msgid "Elementor Experiments"
msgstr "Esperimenti Elementor"

#: core/editor/data/globals/endpoints/base.php:46
msgid "Invalid title"
msgstr "Titolo non valido"

#: core/experiments/manager.php:316
msgid "The “Inline Font Icons” will render the icons as inline SVG without loading the Font-Awesome and the eicons libraries and its related CSS files and fonts."
msgstr "Le \"Icone dei font in linea\" eseguiranno il rendering delle icone come SVG in linea senza caricare le librerie Font-Awesome e eicons e i relativi file CSS e font."

#: includes/settings/tools.php:158
msgid "Not allowed to rollback versions"
msgstr "Non è consentito eseguire il rollback delle versioni"

#: modules/page-templates/module.php:316
msgid "The default page template as defined in Elementor Panel → Hamburger Menu → Site Settings."
msgstr "Il template di pagina predefinito, come definito nel pannello Elementor → Menu Hamburger → Impostazioni del sito."

#: includes/elements/column.php:462 includes/elements/container.php:868
#: includes/elements/section.php:727 includes/widgets/heading.php:339
msgid "Luminosity"
msgstr "Luminosità"

#: includes/elements/column.php:457 includes/elements/container.php:866
#: includes/elements/section.php:725 includes/widgets/heading.php:334
msgid "Saturation"
msgstr "Saturazione"

#: includes/elements/column.php:455 includes/elements/container.php:864
#: includes/elements/section.php:723 includes/widgets/heading.php:332
msgid "Lighten"
msgstr "Schiarisci"

#: core/kits/documents/tabs/settings-page-transitions.php:19
#: includes/managers/controls.php:1120
msgid "Page Transitions"
msgstr "Transizioni pagine"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/history/views/revisions-panel-template.php:31
msgid "Learn more about %1$sWordPress revisions%2$s"
msgstr "Ulteriori informazioni sulle revisioni di %1$sWordPress%2$s"

#. translators: 1: Function argument, 2: Elementor version number.
#: modules/dev-tools/deprecation.php:292
msgid "The %1$s argument is deprecated since version %2$s!"
msgstr "L'argomento %1$s è deprecato dalla versione %2$s!"

#. translators: 1: Function argument, 2: Elementor version number, 3:
#. Replacement argument name.
#: modules/dev-tools/deprecation.php:288
msgid "The %1$s argument is deprecated since version %2$s! Use %3$s instead."
msgstr "L'argomento %1$s è deprecato dalla versione %2$s! Usa invece %3$s."

#: includes/managers/controls.php:1138
msgid "Meet Page Transitions"
msgstr "Incontra le transizioni di pagina"

#: includes/managers/controls.php:1126
msgid "Page Transitions let you style entrance and exit animations between pages as well as display loader until your page assets load."
msgstr "Le transizioni di pagina consentono di applicare uno stile alle animazioni di ingresso e di uscita tra le pagine e di visualizzare il caricatore fino al caricamento delle risorse della pagina."

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: includes/maintenance-mode.php:375
msgid "Select one or go ahead and %1$screate one%2$s now."
msgstr "Selezionane uno o vai avanti e %1$screane uno%2$s ora."

#. translators: %d: Breakpoint screen size.
#: includes/editor-templates/responsive-bar.php:26
msgid "Widescreen <br> Settings added for the Widescreen device will apply to screen sizes %dpx and up"
msgstr "Widescreen <br> Le impostazioni aggiunte per il dispositivo Widescreen verranno applicate alle dimensioni %dpx dello schermo e superiori"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: includes/editor-templates/panel.php:202
msgid "You can enable it from the %1$sElementor settings page%2$s."
msgstr "È possibile abilitarlo dalla pagina delle impostazioni di %1$sElementor%2$s."

#. translators: %s: Device name.
#: includes/base/element-base.php:1393
msgid "Hide On %s"
msgstr "Nascondi su %s"

#. translators: %1$s Span open tag, %2$s: Span close tag.
#: includes/admin-templates/new-template.php:52
msgid "Templates Help You %1$sWork Efficiently%2$s"
msgstr "I template ti aiutano a %1$slavorare in modo efficiente%2$s"

#. translators: 1: Link open tag, 2: Link close tag.
#: core/kits/documents/tabs/tab-base.php:80
msgid "In order for Theme Style to affect all relevant Elementor elements, please disable Default Colors and Fonts from the %1$sSettings Page%2$s."
msgstr "Affinché lo stile del tema influisca su tutti gli elementi Elementor pertinenti, disabilitare colori e caratteri predefiniti dalla %1$sPagina Impostazioni%2$s."

#: core/kits/documents/tabs/settings-layout.php:371
msgid "Widescreen breakpoint settings will apply from the selected value and up."
msgstr "Le impostazioni del punto di interruzione widescreen verranno applicate dal valore selezionato e versioni successive."

#: core/experiments/manager.php:332
msgid "Get pixel-perfect design for every screen size. You can now add up to 6 customizable breakpoints beyond the default desktop setting: mobile, mobile extra, tablet, tablet extra, laptop, and widescreen."
msgstr "Ottieni un design perfetto per ogni dimensione dello schermo. Ora puoi aggiungere fino a 6 punti di interruzione personalizzabili oltre l'impostazione desktop predefinita: mobile, mobile extra, tablet, tablet extra, laptop e widescreen."

#: core/experiments/manager.php:329
msgid "Additional Custom Breakpoints"
msgstr "Punti di interruzione aggiuntivi"

#: includes/elements/column.php:451 includes/elements/container.php:860
#: includes/elements/section.php:719 includes/widgets/heading.php:328
msgid "Multiply"
msgstr "Moltiplica"

#: includes/elements/column.php:453 includes/elements/container.php:862
#: includes/elements/section.php:721 includes/widgets/heading.php:330
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:141
msgid "Overlay"
msgstr "Sovrapponi"

#: includes/elements/column.php:454 includes/elements/container.php:863
#: includes/elements/section.php:722 includes/widgets/heading.php:331
msgid "Darken"
msgstr "Scurisci"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/safe-mode/module.php:290
msgid "Please note! We couldn't deactivate all of your plugins on Safe Mode. Please %1$sread more%2$s about this issue"
msgstr "Nota: non siamo riusciti a disattivare tutti i tuoi plugin in modalità sicura. %1$sLeggi di più%2$s su questo problema"

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/settings/tools.php:403
msgid "%1$sClick here%2$s %3$sto join our first-to-know email updates.%4$s"
msgstr "%1$sFai clic qui%2$s %3$sper unirti ai nostri primi aggiornamenti via email.%4$s"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/safe-mode/module.php:261 modules/safe-mode/module.php:274
msgid "%1$sClick here%2$s to troubleshoot"
msgstr "%1$sFai clic qui%2$s per risolvere i problemi"

#: includes/elements/column.php:456 includes/elements/container.php:865
#: includes/elements/section.php:724 includes/widgets/heading.php:333
msgid "Color Dodge"
msgstr "Scherma il colore"

#: includes/elements/column.php:452 includes/elements/container.php:861
#: includes/elements/section.php:720 includes/widgets/heading.php:329
msgid "Screen"
msgstr "Schermata"

#: includes/managers/elements.php:328
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3281
msgid "Favorites"
msgstr "Preferiti"

#: includes/template-library/sources/admin-menu-items/templates-categories-menu-item.php:23
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3801
msgid "Categories"
msgstr "Categorie"

#: core/common/modules/finder/categories/settings.php:74
#: core/experiments/manager.php:479
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3810
msgid "Features"
msgstr "Caratteristiche"

#: modules/nested-accordion/widgets/nested-accordion.php:554
#: assets/js/app-packages.js:4039
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:60
msgid "Header"
msgstr "Header"

#: modules/library/documents/section.php:47
msgid "Sections"
msgstr "Sezioni"

#: includes/settings/tools.php:435
msgid "It seems like your site doesn't have any active Kit. The active Kit includes all of your Site Settings. By recreating your Kit you will able to start edit your Site Settings again."
msgstr "Sembra che il tuo sito non abbia alcun kit attivo. Il Kit attivo include tutte le impostazioni del sito. Ricreando il tuo Kit potrai ricominciare a modificare le Impostazioni del tuo Sito."

#: includes/settings/tools.php:431 includes/settings/tools.php:434
#: assets/js/editor.js:28888
msgid "Recreate Kit"
msgstr "Ricrea Kit"

#: includes/settings/tools.php:112
msgid "New kit have been created successfully"
msgstr "Il nuovo kit è stato creato con successo"

#: includes/settings/tools.php:107
msgid "An error occurred while trying to create a kit."
msgstr "Si è verificato un errore durante il tentativo di creare un kit."

#: includes/settings/tools.php:101
msgid "There's already an active kit."
msgstr "C'è già un kit attivo."

#: includes/editor-templates/panel.php:291 assets/js/editor.js:13820
msgid "Color Sampler"
msgstr "Campionatore di colori"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:15
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:19
msgid "Submissions"
msgstr "Abbonamenti"

#: app/modules/kit-library/data/repository.php:147
#: app/modules/kit-library/data/repository.php:167
msgid "Kit not found"
msgstr "Kit non trovato"

#: app/modules/kit-library/data/kits/controller.php:29
msgid "Kit not exists."
msgstr "Il kit non esiste."

#: app/modules/kit-library/connect/kit-library.php:16
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:5102
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:5420
msgid "Kit Library"
msgstr "Kit Libreria"

#: app/modules/import-export-customization/module.php:148
#: app/modules/import-export/module.php:150 assets/js/app.js:8919
#: assets/js/app.js:12435
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4407
msgid "Import"
msgstr "Importa"

#: core/common/modules/connect/rest/rest-api.php:139
#: modules/global-classes/global-classes-rest-api.php:223
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:29
msgid "Something went wrong"
msgstr "Qualcosa è andato storto"

#: includes/settings/tools.php:341 assets/js/app.js:8525
msgid "Important:"
msgstr "Importante:"

#: includes/settings/settings.php:318
msgid "API Key"
msgstr "Chiave API"

#: modules/compatibility-tag/compatibility-tag-report.php:172
msgid "Compatibility unknown"
msgstr "Compatibilità sconosciuta"

#: modules/compatibility-tag/compatibility-tag-report.php:171
msgid "Compatibility not specified"
msgstr "Compatibilità non specificata"

#: modules/compatibility-tag/compatibility-tag-report.php:170
msgid "Incompatible"
msgstr "Incompatibile"

#: modules/compatibility-tag/compatibility-tag-report.php:169
msgid "Compatible"
msgstr "Compatibile"

#: includes/settings/settings.php:307
msgid "Google Maps Embed API"
msgstr "Google Maps Embed API"

#. translators: 1: Link open tag, 2: Link close tag
#: includes/settings/settings.php:311
msgid "Google Maps Embed API is a free service by Google that allows embedding Google Maps in your site. For more details, visit Google Maps' %1$sUsing API Keys%2$s page."
msgstr "Google Maps Embed API è un servizio gratuito di Google che consente di incorporare Google Maps nel tuo sito. Per ulteriori dettagli, visita la pagina %1$sUtilizzo delle chiavi API%2$s di Google Maps."

#: includes/widgets/accordion.php:262 includes/widgets/toggle.php:265
#: modules/nested-accordion/widgets/nested-accordion.php:307
msgid "FAQ Schema"
msgstr "Schema FAQ"

#: core/breakpoints/manager.php:339
msgid "Widescreen"
msgstr "Widescreen"

#: core/base/db-upgrades-manager.php:114
msgid "Database update process is running in the background. Taking a while?"
msgstr "Il processo di aggiornamento del database è in esecuzione in background. Ci sta mettendo un po'?"

#: includes/editor-templates/responsive-bar.php:62
msgid "Manage Breakpoints"
msgstr "Gestire i punti di interruzione"

#: includes/widgets/common-base.php:1307
#: modules/floating-buttons/base/widget-contact-button-base.php:2085
#: modules/floating-buttons/base/widget-contact-button-base.php:2176
#: modules/floating-buttons/base/widget-contact-button-base.php:2869
#: modules/floating-buttons/base/widget-floating-bars-base.php:844
#: modules/link-in-bio/base/widget-link-in-bio-base.php:118
msgid "Round"
msgstr "Tondo"

#: includes/controls/groups/background.php:463
#: includes/widgets/common-base.php:1303
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:156
msgid "No-repeat"
msgstr "Non ripetere"

#: includes/controls/groups/background.php:464
#: includes/widgets/common-base.php:1300 includes/widgets/common-base.php:1304
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:153
#: assets/js/packages/editor-controls/editor-controls.strings.js:157
msgid "Repeat"
msgstr "Ripetizione"

#: includes/controls/groups/background.php:385
#: includes/widgets/common-base.php:1264
msgid "Y Position"
msgstr "Posizione Y"

#: includes/controls/groups/background.php:342
#: includes/widgets/common-base.php:1228
msgid "X Position"
msgstr "Posizione X"

#: includes/base/element-base.php:1076 includes/base/element-base.php:1099
#: includes/widgets/common-base.php:1171
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:113
#: assets/js/packages/editor-controls/editor-controls.strings.js:118
msgid "Scale"
msgstr "Scalatura"

#: includes/widgets/common-base.php:1156
msgid "Fit"
msgstr "Adatta"

#: includes/widgets/common-base.php:1136
msgid "Need More Shapes?"
msgstr "Hai bisogno di più forme?"

#: includes/widgets/common-base.php:1081 includes/widgets/common-base.php:1089
msgid "Mask"
msgstr "Maschera"

#: includes/widgets/common-base.php:212
msgid "Blob"
msgstr "Macchia"

#: includes/widgets/common-base.php:156
msgid "Triangle"
msgstr "Triangolo"

#: includes/widgets/common-base.php:204
msgid "Sketch"
msgstr "Schizzo"

#: includes/widgets/common-base.php:200
msgid "Flower"
msgstr "Fiore"

#: includes/settings/settings.php:392
msgid "Font-display property defines how font files are loaded and displayed by the browser."
msgstr "La proprietà font-display definisce la modalità di caricamento e visualizzazione dei file dei tipi di carattere da parte del browser."

#: includes/settings/settings.php:390
msgid "Optional"
msgstr "Opzionale"

#: includes/settings/settings.php:388
msgid "Swap"
msgstr "Scambia"

#: includes/settings/settings.php:387
msgid "Blocking"
msgstr "Blocco"

#: core/admin/admin-notices.php:367
msgid "With Elementor Pro, you can control user access and make sure no one messes up your design."
msgstr "Con Elementor Pro, puoi controllare l'accesso degli utenti e assicurarti che nessuno incasini il tuo design."

#: core/admin/admin-notices.php:366
msgid "Managing a multi-user site?"
msgstr "Gestisci un sito utente multisite?"

#: includes/controls/groups/background.php:276
#: includes/controls/groups/background.php:328
#: includes/controls/groups/background.php:716
#: includes/widgets/common-base.php:1214 includes/widgets/image-box.php:434
#: includes/widgets/image.php:409
#: modules/link-in-bio/base/widget-link-in-bio-base.php:188
msgid "Bottom Right"
msgstr "In basso a destra"

#: includes/controls/groups/background.php:274
#: includes/controls/groups/background.php:326
#: includes/controls/groups/background.php:714
#: includes/widgets/common-base.php:1212 includes/widgets/image-box.php:432
#: includes/widgets/image.php:407
#: modules/link-in-bio/base/widget-link-in-bio-base.php:186
msgid "Bottom Center"
msgstr "Centrato Sotto"

#: includes/controls/groups/background.php:275
#: includes/controls/groups/background.php:327
#: includes/controls/groups/background.php:715
#: includes/widgets/common-base.php:1213 includes/widgets/image-box.php:433
#: includes/widgets/image.php:408
#: modules/link-in-bio/base/widget-link-in-bio-base.php:187
msgid "Bottom Left"
msgstr "In basso a sinistra"

#: includes/controls/groups/background.php:270
#: includes/controls/groups/background.php:322
#: includes/controls/groups/background.php:710
#: includes/widgets/common-base.php:1208 includes/widgets/image-box.php:428
#: includes/widgets/image.php:403
#: modules/link-in-bio/base/widget-link-in-bio-base.php:182
msgid "Center Right"
msgstr "Centrato a Destra"

#: includes/controls/groups/background.php:268
#: includes/controls/groups/background.php:320
#: includes/controls/groups/background.php:708
#: includes/widgets/common-base.php:1206 includes/widgets/image-box.php:426
#: includes/widgets/image.php:401
#: modules/link-in-bio/base/widget-link-in-bio-base.php:180
msgid "Center Center"
msgstr "Centrato"

#: includes/controls/groups/background.php:269
#: includes/controls/groups/background.php:321
#: includes/controls/groups/background.php:709
#: includes/widgets/common-base.php:1207 includes/widgets/image-box.php:427
#: includes/widgets/image.php:402
#: modules/link-in-bio/base/widget-link-in-bio-base.php:181
msgid "Center Left"
msgstr "Centrato a Sinistra"

#: includes/controls/groups/background.php:273
#: includes/controls/groups/background.php:325
#: includes/controls/groups/background.php:713
#: includes/widgets/common-base.php:1211 includes/widgets/image-box.php:431
#: includes/widgets/image.php:406
#: modules/link-in-bio/base/widget-link-in-bio-base.php:185
msgid "Top Right"
msgstr "Sopra a Destra"

#: includes/controls/groups/background.php:271
#: includes/controls/groups/background.php:323
#: includes/controls/groups/background.php:711
#: includes/widgets/common-base.php:1209 includes/widgets/image-box.php:429
#: includes/widgets/image.php:404
#: modules/link-in-bio/base/widget-link-in-bio-base.php:183
msgid "Top Center"
msgstr "Sopra al Centro"

#: includes/controls/groups/background.php:272
#: includes/controls/groups/background.php:324
#: includes/controls/groups/background.php:712
#: includes/widgets/common-base.php:1210 includes/widgets/image-box.php:430
#: includes/widgets/image.php:405
#: modules/link-in-bio/base/widget-link-in-bio-base.php:184
msgid "Top Left"
msgstr "Sopra a Sinistra"

#: includes/editor-templates/responsive-bar.php:22
msgid "Desktop <br> Settings added for the base device will apply to all breakpoints unless edited"
msgstr "Desktop <br> Le impostazioni aggiunte per il dispositivo di base verranno applicate a tutti i punti di interruzione, a meno che non vengano modificati"

#: includes/controls/groups/background.php:465
#: includes/widgets/common-base.php:1305
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:154
msgid "Repeat-x"
msgstr "Ripeti orizzontalmente"

#: includes/controls/groups/background.php:466
#: includes/widgets/common-base.php:1306
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:155
msgid "Repeat-y"
msgstr "Ripeti verticalmente"

#. translators: %1$s: Device name, %2$s: Breakpoint screen size.
#: includes/editor-templates/responsive-bar.php:32
msgid "%1$s <br> Settings added for the %1$s device will apply to %2$spx screens and down"
msgstr "%1$s <br> Le impostazioni aggiunte per il dispositivo %1$s verranno applicate agli schermi da %2$spx in giù"

#: includes/settings/settings.php:381
msgid "Google Fonts Load"
msgstr "Caricamento dei Google Fonts"

#: includes/settings/settings.php:392
msgid "Set the way Google Fonts are being loaded by selecting the font-display property (Recommended: Swap)."
msgstr "Imposta il modo in cui vengono caricati i Google Fonts selezionando la proprietà font-display (Raccomandato: Scambia)."

#: app/modules/import-export-customization/module.php:197
#: app/modules/import-export/module.php:199 core/admin/admin-notices.php:284
#: core/admin/admin-notices.php:412 core/admin/admin-notices.php:503
#: core/admin/admin-notices.php:551 core/admin/admin-notices.php:599
#: core/experiments/manager.php:317 core/experiments/manager.php:333
#: core/experiments/manager.php:362 core/experiments/manager.php:533
#: includes/controls/url.php:78 includes/elements/section.php:473
#: includes/settings/settings-page.php:404
#: includes/widgets/common-base.php:1141 includes/widgets/video.php:606
#: modules/ai/feature-intro/product-image-unification-intro.php:40
#: modules/checklist/steps/step-base.php:102
#: modules/nested-elements/module.php:21
#: modules/shapes/widgets/text-path.php:150 assets/js/app.js:4734
#: assets/js/app.js:8515 assets/js/editor-v4-opt-in-alphachip.js:187
#: assets/js/editor-v4-opt-in.js:497 assets/js/editor-v4-welcome-opt-in.js:84
#: assets/js/editor.js:27990
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4449
msgid "Learn more"
msgstr "Scopri di più"

#: core/kits/documents/tabs/settings-layout.php:217
msgid "Mobile and Tablet options cannot be deleted."
msgstr "Le opzioni per dispositivi mobili e tablet non possono essere eliminate."

#: core/kits/documents/tabs/settings-layout.php:215
msgid "Active Breakpoints"
msgstr "Punti di interruzione attivi"

#: core/utils/import-export/parsers/wxr-parser-regex.php:146
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:70
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:76
#: core/utils/import-export/parsers/wxr-parser-xml.php:190
msgid "This does not appear to be a WXR file, missing/invalid WXR version number"
msgstr "Questo non sembra essere un file WXR; numero di versione WXR mancante/non valido"

#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:57
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:65
msgid "There was an error when reading this WXR file"
msgstr "Si è verificato un errore durante la lettura di questo file WXR"

#: core/utils/import-export/wp-import.php:1143
msgid "The uploaded file could not be moved"
msgstr "Non è stato possibile spostare il file caricato"

#: core/utils/import-export/wp-import.php:1127
msgid "Sorry, this file type is not permitted for security reasons."
msgstr "Spiacenti, questo tipo di file non è consentito per motivi di sicurezza."

#. translators: %s: Max file size.
#: core/utils/import-export/wp-import.php:1095
msgid "Remote file is too large, limit is %s"
msgstr "File remoto troppo grande, il limite è %s"

#: core/utils/import-export/wp-import.php:1087
msgid "Downloaded file has incorrect size"
msgstr "Il file scaricato ha una dimensione errata"

#: core/utils/import-export/wp-import.php:1081
msgid "Zero size file downloaded"
msgstr "Dimensione del file scaricato uguale a zero"

#: core/utils/import-export/wp-import.php:1073
msgid "Remote server did not respond"
msgstr "Il server remoto non risponde"

#. translators: 1: HTTP error message, 2: HTTP error code.
#: core/utils/import-export/wp-import.php:1064
msgid "Remote server returned the following unexpected result: %1$s (%2$s)"
msgstr "Il server remoto ha restituito il seguente risultato inaspettato: %1$s (%2$s)"

#. translators: 1: WordPress error message, 2: WordPress error code.
#: core/utils/import-export/wp-import.php:1055
msgid "Request failed due to an error: %1$s (%2$s)"
msgstr "Richiesta fallita a causa di un errore: %1$s (%2$s)"

#: core/utils/import-export/wp-import.php:1039
msgid "Could not create temporary file."
msgstr "Impossibile creare il file temporaneo."

#: core/utils/import-export/wp-import.php:995
msgid "Invalid file type"
msgstr "Tipo di file non valido"

#: core/utils/import-export/wp-import.php:978
msgid "Fetching attachments is not enabled"
msgstr "Il recupero degli allegati non è abilitato"

#. translators: %s: Menu slug.
#: core/utils/import-export/wp-import.php:891
msgid "Menu item skipped due to invalid menu slug: %s"
msgstr "Voce di menu saltata a causa di un errore di menu slug non valido: %s"

#: core/utils/import-export/wp-import.php:878
msgid "Menu item skipped due to missing menu slug"
msgstr "Voce di menu saltato a causa della mancanza di uno slug"

#. translators: 1: Post title, 2: Post type.
#: core/utils/import-export/wp-import.php:584
msgid "Failed to import %1$s: Invalid post type %2$s"
msgstr "Impossibile importare %1$s: tipo di post non valido %2$s"

#. translators: 1: Term taxonomy, 2: Term name.
#. translators: 1: Post type singular label, 2: Post title.
#. translators: 1: Taxonomy name, 2: Term name.
#: core/utils/import-export/wp-import.php:485
#: core/utils/import-export/wp-import.php:676
#: core/utils/import-export/wp-import.php:726
msgid "Failed to import %1$s %2$s"
msgstr "Impossibile importare %1$s %2$s"

#: core/utils/import-export/wp-import.php:253
msgid "The file does not exist, please try again."
msgstr "Questo file non esiste, prova di nuovo."

#: modules/shapes/module.php:48
msgid "Spiral"
msgstr "Spirale"

#: modules/shapes/module.php:47
msgid "Oval"
msgstr "Ovale"

#: modules/shapes/module.php:44
msgid "Arc"
msgstr "Arco"

#: modules/shapes/module.php:43
msgid "Wave"
msgstr "Onda"

#: modules/shapes/widgets/text-path.php:485
msgid "Path"
msgstr "Percorso"

#: modules/shapes/widgets/text-path.php:382
msgid "Starting Point"
msgstr "Punto di partenza"

#: includes/controls/groups/typography.php:245
#: modules/shapes/widgets/text-path.php:347
msgid "Word Spacing"
msgstr "Spaziatura parole"

#: modules/shapes/widgets/text-path.php:217
msgid "Show Path"
msgstr "Mostra percorso"

#: modules/shapes/widgets/text-path.php:205
msgid "LTR"
msgstr "LTR"

#: modules/shapes/widgets/text-path.php:204
msgid "RTL"
msgstr "RTL"

#: modules/shapes/widgets/text-path.php:199
msgid "Text Direction"
msgstr "Direzione testo"

#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:34
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:60
#: modules/shapes/widgets/text-path.php:136
msgid "SVG"
msgstr "SVG"

#: modules/shapes/widgets/text-path.php:126
msgid "Path Type"
msgstr "Tipo di percorso"

#: modules/shapes/widgets/text-path.php:114
msgid "Add Your Curvy Text Here"
msgstr "Aggiungi qui il tuo testo curvo"

#. translators: %s: Author display name.
#: core/utils/import-export/wp-import.php:385
msgid "Failed to create new user for %s. Their posts will be attributed to the current user."
msgstr "Impossibile creare un nuovo utente per %s. I suoi articoli saranno attribuiti all’utente corrente."

#. translators: %s: Post author.
#: core/utils/import-export/wp-import.php:320
msgid "Failed to import author %s. Their posts will be attributed to the current user."
msgstr "È fallita l’importazione dell’autore %s. I suoi articoli sono stati attribuiti all’utente corrente."

#: modules/shapes/widgets/text-path.php:520
#: modules/shapes/widgets/text-path.php:591
msgid "Stroke"
msgstr "Contorno"

#: modules/shapes/widgets/text-path.php:51
#: modules/shapes/widgets/text-path.php:103
#: modules/shapes/widgets/text-path.php:243
msgid "Text Path"
msgstr "Percorso del testo"

#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:86
msgid "Privacy mode"
msgstr "Modalità riservata"

#: core/experiments/manager.php:678
msgid "Inactive by default"
msgstr "Inattivo per impostazione predefinita"

#: core/experiments/manager.php:677
msgid "Active by default"
msgstr "Attivo per impostazione predefinita"

#. translators: %s Release status.
#: core/experiments/manager.php:564
msgid "Status: %s"
msgstr "Stato: %s"

#: core/common/modules/finder/categories/settings.php:69
msgid "Experiments"
msgstr "Esperimenti"

#: core/experiments/manager.php:467
msgid "The current version of Elementor doesn't have any experimental features . if you're feeling curious make sure to come back in future versions."
msgstr "La versione corrente di Elementor non dispone di funzionalità sperimentali. Se ti senti curioso assicurati di tornare nelle versioni future."

#: core/experiments/manager.php:464
msgid "No available experiments"
msgstr "Nessun esperimento disponibile"

#: core/experiments/manager.php:406
msgid "Stable"
msgstr "Stabile"

#: core/experiments/manager.php:405 assets/js/ai-admin.js:655
#: assets/js/ai-admin.js:7764 assets/js/ai-gutenberg.js:793
#: assets/js/ai-gutenberg.js:7982 assets/js/ai-layout.js:487
#: assets/js/ai-layout.js:3255 assets/js/ai-media-library.js:655
#: assets/js/ai-media-library.js:7764 assets/js/ai-unify-product-images.js:655
#: assets/js/ai-unify-product-images.js:7764 assets/js/ai.js:1438
#: assets/js/ai.js:9225
msgid "Beta"
msgstr "Beta"

#: core/experiments/manager.php:404 modules/atomic-widgets/module.php:303
#: assets/js/editor-v4-opt-in.js:345 assets/js/editor-v4-opt-in.js:492
msgid "Alpha"
msgstr "Alpha"

#: core/experiments/manager.php:403
msgid "Development"
msgstr "Sviluppo"

#: core/experiments/manager.php:532
msgid "To use an experiment or feature on your site, simply click on the dropdown next to it and switch to Active. You can always deactivate them at any time."
msgstr "Per utilizzare un esperimento o una funzionalità sul tuo sito, fai semplicemente clic sull'elenco a discesa accanto ad esso e passa ad Attivo. Puoi sempre disattivarli in qualsiasi momento."

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:42
#: assets/js/element-manager-admin.js:671
#: assets/js/element-manager-admin.js:732
msgid "Plugin"
msgstr "Plugin"

#: modules/landing-pages/module.php:304
msgid "No landing pages found in trash"
msgstr "Nessuna landing page trovata nel cestino"

#: modules/landing-pages/module.php:303
msgid "No landing pages found"
msgstr "Nessuna landing page trovata"

#: modules/landing-pages/module.php:302
msgid "Search Landing Pages"
msgstr "Cerca Landing Page"

#: modules/landing-pages/module.php:301
msgid "View Landing Page"
msgstr "Visualizza Landing Page"

#: modules/landing-pages/module.php:300
msgid "All Landing Pages"
msgstr "Tutte le Landing Page"

#: modules/landing-pages/module.php:299
msgid "New Landing Page"
msgstr "Nuova Landing Page"

#: modules/landing-pages/module.php:298
msgid "Edit Landing Page"
msgstr "Modifica Landing Page"

#: modules/landing-pages/module.php:297
msgid "Add New Landing Page"
msgstr "Aggiungi nuova Landing Page"

#: modules/landing-pages/module.php:229
msgid "Build Effective Landing Pages for your business' marketing campaigns."
msgstr "Crea landing page efficaci per le campagne di marketing della tua azienda."

#: modules/landing-pages/module.php:47
msgid "Adds a new Elementor content type that allows creating beautiful landing pages instantly in a streamlined workflow."
msgstr "Aggiunge un nuovo tipo di contenuto Elementor che consente di creare immediatamente belle pagine di destinazione in un flusso di lavoro semplificato."

#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:22
#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:26
#: modules/landing-pages/documents/landing-page.php:54
#: modules/landing-pages/module.php:46 modules/landing-pages/module.php:163
#: modules/landing-pages/module.php:294 modules/landing-pages/module.php:306
#: assets/js/app.js:6328 assets/js/app.js:11352 assets/js/editor.js:53407
msgid "Landing Pages"
msgstr "Landing Page"

#: modules/landing-pages/documents/landing-page.php:46
#: modules/landing-pages/module.php:229 modules/landing-pages/module.php:295
msgid "Landing Page"
msgstr "Landing Page"

#: modules/compatibility-tag/compatibility-tag-report.php:123
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:57
#: modules/element-manager/ajax.php:139
msgid "Unknown"
msgstr "Sconosciuto"

#. translators: %s: Elementor plugin name.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:45
msgid "Tested up to %s version"
msgstr "Testato fino a versione %s"

#. translators: 1: Plugin name, 2: Plugin version.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:33
msgid "Some of the plugins you’re using have not been tested with the latest version of %1$s (%2$s). To avoid issues, make sure they are all up to date and compatible before updating %1$s."
msgstr "Alcuni dei plugin che stai utilizzando non sono stati testati con l'ultima versione di %1$s (%2$s). Per evitare problemi, assicurarsi che siano tutti aggiornati e compatibili prima di aggiornarli %1$s."

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:28
msgid "Compatibility Alert"
msgstr "Avviso compatibilità"

#: includes/elements/section.php:301
msgid "Custom Columns Gap"
msgstr "Spaziatura colonne personalizzato"

#: core/kits/views/trash-kit-confirmation.php:33
msgid "Keep my settings"
msgstr "Mantieni le mie impostazioni"

#: core/kits/views/trash-kit-confirmation.php:21
msgid "By removing this template you will delete your entire Site Settings. If this template is deleted, all associated settings: Global Colors & Fonts, Theme Style, Layout, Background, and Lightbox settings will be removed from your existing site. This action can not be undone."
msgstr "Rimuovendo questo template eliminerai tutte le impostazioni del sito. Se questo template viene eliminato, tutte le impostazioni associate: Colori e caratteri globali, Stile tema, Layout, Sfondo e impostazioni Lightbox verranno rimosse dai siti esistenti. Questa azione non può essere annullata."

#: core/kits/views/trash-kit-confirmation.php:17
msgid "Are you sure you want to delete your Site Settings?"
msgstr "Sei sicuro di voler eliminare le impostazioni del tuo sito?"

#: core/editor/data/globals/endpoints/base.php:34
msgid "The Global value you are trying to use is not available."
msgstr "Il valore Globale che stai tentando di utilizzare non è disponibile."

#. Description of the plugin
#: elementor.php
msgid "The Elementor Website Builder has it all: drag and drop page builder, pixel perfect design, mobile responsive editing, and more. Get started now!"
msgstr "Elementor Website Builder ha tutto: drag and drop page builder, pixel design perfetto, mobile responsive editing e altro ancora. Inizia subito!"

#: includes/controls/media.php:195
msgid "Choose SVG"
msgstr "Scegli Svg"

#: core/kits/documents/tabs/global-colors.php:24
#: core/kits/documents/tabs/global-colors.php:43 assets/js/app.js:6344
#: assets/js/app.js:11360 assets/js/editor.js:47673
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:173
msgid "Global Colors"
msgstr "Colori globali"

#: core/kits/documents/tabs/settings-layout.php:350
#: modules/nested-tabs/widgets/nested-tabs.php:432
msgid "Breakpoint"
msgstr "Punto di Interruzione"

#: core/kits/documents/tabs/settings-layout.php:183
#: modules/page-templates/module.php:159
msgid "Theme"
msgstr "Tema"

#: core/kits/manager.php:436 includes/editor-templates/hotkeys.php:112
#: assets/js/app.js:11358 assets/js/app.js:11855 assets/js/editor.js:47622
#: assets/js/editor.js:47626 assets/js/editor.js:47636
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:9
msgid "Site Settings"
msgstr "Impostazioni del sito"

#: core/kits/documents/tabs/settings-site-identity.php:118
msgid "Site Favicon"
msgstr "Favicon del sito"

#: core/kits/documents/tabs/settings-site-identity.php:93
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:997
msgid "Site Logo"
msgstr "Logo del sito"

#: core/kits/documents/tabs/settings-site-identity.php:84
msgid "Choose description"
msgstr "Scegli la descrizione"

#: core/kits/documents/tabs/settings-site-identity.php:82
msgid "Site Description"
msgstr "Descrizione del sito"

#: core/kits/documents/tabs/settings-site-identity.php:73
msgid "Choose name"
msgstr "Scegli il nome"

#: core/kits/documents/tabs/settings-site-identity.php:71
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:993
msgid "Site Name"
msgstr "Nome del sito"

#: includes/controls/groups/flex-container.php:24
#: includes/controls/groups/grid-container.php:26
#: includes/widgets/icon-list.php:180
#: modules/nested-accordion/widgets/nested-accordion.php:146
msgid "Items"
msgstr "Elementi"

#: includes/widgets/common-base.php:1157 includes/widgets/image-box.php:406
#: includes/widgets/image.php:383
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:131
msgid "Fill"
msgstr "Riempi"

#: includes/frontend.php:1380
msgid "Download"
msgstr "Download"

#: includes/widgets/image-box.php:402 includes/widgets/image.php:376
msgid "Object Fit"
msgstr "Adatta oggetto"

#: core/admin/admin.php:867
msgid "Heads up, Please backup before upgrade!"
msgstr "Attenzione, Si prega di eseguire il backup prima dell'aggiornamento!"

#: core/settings/editor-preferences/model.php:38
#: includes/editor-templates/hotkeys.php:146 assets/js/editor.js:38592
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:6
msgid "User Preferences"
msgstr "Preferenze utente"

#: modules/nested-tabs/widgets/nested-tabs.php:371 assets/js/editor.js:48032
msgid "Additional Settings"
msgstr "Impostazioni aggiuntive"

#: core/settings/editor-preferences/model.php:174 assets/js/editor.js:48040
msgid "Design System"
msgstr "Sistema di progettazione"

#: core/kits/documents/tabs/global-typography.php:197
msgid "Fallback Font Family"
msgstr "Famiglia di caratteri di fallback"

#: core/kits/documents/tabs/settings-background.php:67
msgid "The `theme-color` meta tag will only be available in supported browsers and devices."
msgstr "Il meta tag 'theme-color' sarà disponibile solo nei browser e nei dispositivi supportati."

#: core/kits/documents/tabs/settings-background.php:65
msgid "Mobile Browser Background"
msgstr "Sfondo del browser per dispositivi mobili"

#: core/kits/documents/tabs/settings-site-identity.php:125
msgid "Suggested favicon dimensions: 512 × 512 pixels."
msgstr "Dimensioni favicon suggerite: 512 × 512 pixel."

#: core/kits/documents/tabs/settings-layout.php:194
msgid "Breakpoints"
msgstr "Breakpoint"

#: core/kits/documents/tabs/settings-layout.php:180
msgid "Default Page Layout"
msgstr "Layout di pagina predefinito"

#: core/kits/documents/tabs/settings-layout.php:47 assets/js/app.js:6344
#: assets/js/app.js:11360
msgid "Layout Settings"
msgstr "Impostazioni Layout"

#: modules/page-templates/module.php:365
msgid "Changes will be reflected in the preview only after the page reloads."
msgstr "Le modifiche verranno riflesse nell'anteprima solo dopo il ricaricamento della pagina."

#: app/modules/kit-library/module.php:135
#: core/frontend/render-mode-manager.php:152
#: modules/compatibility-tag/compatibility-tag-report.php:173
msgid "Error"
msgstr "Errore"

#: core/kits/documents/tabs/settings-site-identity.php:20
msgid "Site Identity"
msgstr "Denominazione del sito"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: core/admin/admin.php:873
msgid "The latest update includes some substantial changes across different areas of the plugin. We highly recommend you %1$sbackup your site before upgrading%2$s, and make sure you first update in a staging environment"
msgstr "L'ultimo aggiornamento include alcune modifiche sostanziali in diverse aree del plugin. Ti consigliamo vivamente di eseguire %1$sbackup del tuo sito prima di eseguire l'aggiornamento%2$s e assicurati di eseguire prima l'aggiornamento in un ambiente di gestione temporanea"

#. translators: 1: Width number pixel, 2: Height number pixel.
#: core/kits/documents/tabs/settings-site-identity.php:102
msgid "Suggested image dimensions: %1$s × %2$s pixels."
msgstr "Dimensioni dell'immagine suggerite: %1$s × %2$s pixel."

#: includes/widgets/social-icons.php:485
msgid "Rows Gap"
msgstr "Spaziatura righe"

#: includes/widgets/icon-list.php:213
msgid "Apply Link On"
msgstr "Applica al Link"

#: core/common/modules/connect/apps/base-app.php:109
msgid "Reset Data"
msgstr "Reimposta i dati"

#: includes/controls/media.php:270
msgid "Click the media icon to upload file"
msgstr "Fare clic sull'icona dei media per caricare il file"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:74
msgid "Watch the Full Guide"
msgstr "Guarda la guida completa"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:62
msgid "Get introduced to Elementor by watching our \"Getting Started\" video series. It will guide you through the steps needed to create your website. Then click to create your first page."
msgstr "Fatevi presentare Elementor guardando la nostra serie di video \"Iniziare\". Vi guiderà attraverso i passi necessari per creare il vostro sito web. Poi clicca per creare la tua prima pagina."

#: includes/settings/settings.php:353 assets/js/admin.js:294
#: assets/js/admin.js:304 assets/js/ai-admin.js:64 assets/js/ai-admin.js:74
#: assets/js/ai-gutenberg.js:64 assets/js/ai-gutenberg.js:74
#: assets/js/ai-media-library.js:64 assets/js/ai-media-library.js:74
#: assets/js/ai-unify-product-images.js:64
#: assets/js/ai-unify-product-images.js:74 assets/js/ai.js:64
#: assets/js/ai.js:74 assets/js/common.js:64 assets/js/common.js:74
#: assets/js/editor.js:40406 assets/js/editor.js:40416
msgid "Enable Unfiltered File Uploads"
msgstr "Abilita caricamento di file non filtrato"

#: modules/safe-mode/module.php:367
msgid "If you are experiencing a loading issue, contact your site administrator to troubleshoot the problem using Safe Mode."
msgstr "Se si verifica un problema di caricamento, contatta l'amministratore del sito per risolvere il problema utilizzando la Modalità sicura."

#: includes/frontend.php:1381
msgid "Download image"
msgstr "Scarica immagine"

#: includes/frontend.php:1379
msgid "Pin it"
msgstr "Pinterest"

#: includes/frontend.php:1378
msgid "Share on Twitter"
msgstr "Condividi su Twitter"

#: includes/frontend.php:1377
msgid "Share on Facebook"
msgstr "Condividi su Facebook"

#: includes/controls/url.php:119
msgid "Custom Attributes"
msgstr "Attributi Personalizzati"

#: includes/editor-templates/panel.php:315
msgid "Get more dynamic capabilities by incorporating dozens of Elementor's native dynamic tags."
msgstr "Ottenete maggiori capacità dinamiche incorporando dozzine di tag dinamici nativi di Elementor."

#: includes/editor-templates/panel.php:314
msgid "You’re missing out!"
msgstr "Vi state perdendo!"

#: includes/editor-templates/panel.php:311
msgid "Elementor Dynamic Content"
msgstr "Contenuto dinamico di Elementor"

#: includes/editor-templates/panel.php:285
msgid "Dynamic Tags"
msgstr "Tag dinamici"

#: includes/managers/icons.php:491
msgid "We highly recommend backing up your database before performing this upgrade."
msgstr "Si consiglia vivamente di eseguire il backup del database prima di eseguire questo aggiornamento."

#: includes/managers/icons.php:490
msgid "The upgrade process includes a database update"
msgstr "Il processo di aggiornamento include un aggiornamento del database"

#: includes/managers/controls.php:1216
msgid "Attributes lets you add custom HTML attributes to any element."
msgstr "Attributi consente di aggiungere attributi HTML personalizzati a qualsiasi elemento."

#: includes/managers/controls.php:1214
msgid "Meet Our Attributes"
msgstr "Incontra i nostri attributi"

#: includes/managers/controls.php:1204
msgid "Attributes"
msgstr "Attributi"

#: core/base/db-upgrades-manager.php:118
msgid "Click here to run it now"
msgstr "Clicca qui per eseguirlo ora"

#: core/kits/documents/tabs/settings-lightbox.php:187
msgid "Navigation Icons Size"
msgstr "Dimensione delle icone di navigazione"

#: core/kits/documents/tabs/settings-lightbox.php:174
msgid "Toolbar Icons Size"
msgstr "Dimensioni icone barra degli strumenti"

#: core/kits/documents/tabs/settings-lightbox.php:102
#: core/kits/documents/tabs/settings-lightbox.php:119
msgid "Alt"
msgstr "Alt"

#: core/kits/documents/tabs/settings-lightbox.php:86 includes/frontend.php:1384
msgid "Share"
msgstr "Condividi"

#: core/kits/documents/tabs/settings-lightbox.php:66 includes/frontend.php:1382
msgid "Fullscreen"
msgstr "Schermo intero"

#. translators: %s: Widget title.
#: core/editor/promotion.php:56
msgid "Use %s widget and dozens more pro features to extend your toolbox and build sites faster and better."
msgstr "Utilizzate il widget %s e altre decine di funzioni pro per estendere la vostra cassetta degli attrezzi e costruire siti in modo più veloce e migliore."

#. translators: %s: Widget title.
#: core/editor/promotion.php:54
msgid "%s Widget"
msgstr "%s Widget"

#: core/isolation/elementor-adapter.php:28 core/kits/manager.php:156
#: core/kits/manager.php:174
msgid "Default Kit"
msgstr "Kit predefinito"

#: core/kits/documents/tabs/theme-style-form-fields.php:128
msgid "Focus"
msgstr "Focus"

#: core/kits/documents/tabs/theme-style-form-fields.php:99
msgid "Field"
msgstr "Campo"

#: core/kits/documents/tabs/theme-style-form-fields.php:71
msgid "Label"
msgstr "Etichetta"

#: core/kits/documents/tabs/theme-style-form-fields.php:21
#: core/kits/documents/tabs/theme-style-form-fields.php:60
msgid "Form Fields"
msgstr "Campi modulo"

#: core/kits/documents/tabs/theme-style-typography.php:75
#: includes/widgets/text-editor.php:306
msgid "Paragraph Spacing"
msgstr "Spaziatura paragrafo"

#: core/kits/documents/tabs/theme-style-typography.php:48
msgid "Body"
msgstr "Corpo"

#: core/kits/documents/tabs/theme-style-buttons.php:23
#: core/kits/documents/tabs/theme-style-buttons.php:63
#: modules/floating-buttons/base/widget-contact-button-base.php:221
msgid "Buttons"
msgstr "Pulsanti"

#: core/kits/documents/kit.php:154
msgid "Draft"
msgstr "Bozza"

#: core/kits/documents/kit.php:43
msgid "Kit"
msgstr "Kit"

#: core/experiments/manager.php:104 includes/editor-templates/global.php:27
#: includes/editor-templates/templates.php:232 assets/js/ai-admin.js:9553
#: assets/js/ai-gutenberg.js:9771 assets/js/ai-media-library.js:9553
#: assets/js/ai-unify-product-images.js:9553 assets/js/ai.js:11014
#: assets/js/app.js:7995 assets/js/editor.js:47928
msgid "Back"
msgstr "Indietro"

#: includes/controls/url.php:77
msgid "Set custom attributes for the link element. Separate attribute keys from values using the | (pipe) character. Separate key-value pairs with a comma."
msgstr "Imposta attributi personalizzati per l'elemento link. Separa le chiavi degli attributi dai valori utilizzando il carattere | (pipe). Separa le coppie chiave-valore con una virgola."

#: core/common/modules/connect/apps/base-app.php:161
msgid "Already connected."
msgstr "Già collegato."

#: core/settings/editor-preferences/model.php:51
msgid "Preferences"
msgstr "Preferenze"

#: includes/widgets/image-carousel.php:473
msgid "Pause on Interaction"
msgstr "Pausa su interazione"

#. translators: %s: Video provider
#: includes/embed.php:185
msgid "%s Video Player"
msgstr "%s Video Player"

#: includes/controls/groups/background.php:702
msgid "Background Position"
msgstr "Posizione Sfondo"

#: core/kits/documents/tabs/settings-background.php:81
#: includes/controls/groups/background.php:486
#: includes/controls/groups/background.php:691
#: includes/widgets/image-box.php:408 includes/widgets/image.php:385
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:150
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:133
msgid "Contain"
msgstr "Contenitore"

#: includes/controls/groups/background.php:485
#: includes/controls/groups/background.php:690
#: includes/widgets/image-box.php:407 includes/widgets/image.php:384
#: modules/link-in-bio/base/widget-link-in-bio-base.php:923
#: modules/link-in-bio/base/widget-link-in-bio-base.php:978
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:149
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:132
msgid "Cover"
msgstr "Cover"

#: core/kits/documents/tabs/settings-background.php:80
#: includes/controls/groups/background.php:484
#: includes/controls/groups/background.php:689
#: includes/elements/container.php:561 includes/widgets/social-icons.php:299
#: includes/widgets/video.php:600
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:80
#: assets/js/packages/editor-controls/editor-controls.strings.js:148
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:129
msgid "Auto"
msgstr "Automatica"

#: includes/controls/groups/background.php:683
msgid "Background Size"
msgstr "Sfondo Dimensione"

#: core/settings/editor-preferences/model.php:152 assets/js/ai-admin.js:15884
#: assets/js/ai-gutenberg.js:16102 assets/js/ai-layout.js:5202
#: assets/js/ai-media-library.js:15884
#: assets/js/ai-unify-product-images.js:15884 assets/js/ai.js:7744
#: assets/js/ai.js:17345 assets/js/editor.js:8001
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:446
msgid "Get Started"
msgstr "Inizia"

#. translators: %s: Remote user.
#: core/common/modules/connect/apps/base-app.php:84
msgid "Connected as %s"
msgstr "Collegato come %s"

#: core/common/modules/connect/apps/library.php:29
#: core/common/modules/connect/apps/library.php:56
msgid "Connecting to the Library failed. Please try reloading the page and try again"
msgstr "Collegamento alla Libreria non riuscito. Prova a ricaricare la pagina e riprova"

#: core/settings/editor-preferences/model.php:78
msgid "Auto detect"
msgstr "Auto detect"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:19
msgid "Custom Icons"
msgstr "Icone personalizzate"

#: includes/controls/groups/background.php:655
msgid "Transition"
msgstr "Transizione"

#: includes/controls/groups/background.php:645
msgid "Duration"
msgstr "Durata"

#: core/logger/log-reporter.php:44
msgid "Clear Log"
msgstr "Cancella il log"

#: includes/settings/tools.php:386
msgid "Reinstall"
msgstr "Reinstalla"

#: includes/controls/groups/background.php:528
msgid "YouTube/Vimeo link, or link to video file (mp4 is recommended)."
msgstr "Link YouTube/Vimeo o file video (mp4 è raccomandato)"

#: includes/controls/groups/background.php:608
msgid "This cover image will replace the background video in case that the video could not be loaded."
msgstr "Questa immagine di copertina sostituirà il video di sfondo nel caso in cui il video non viene caricato."

#: includes/controls/groups/background.php:738
msgid "Ken Burns Effect"
msgstr "Effetto Ken Burns"

#: includes/widgets/image-gallery.php:44 includes/widgets/image-gallery.php:130
msgid "Basic Gallery"
msgstr "Galleria di base"

#: includes/controls/groups/background.php:754
msgid "Out"
msgstr "Fuori"

#: includes/controls/groups/background.php:753
msgid "In"
msgstr "Dentro"

#: includes/widgets/divider.php:496
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:21
msgid "Add Element"
msgstr "Aggiungi elemento"

#: includes/frontend.php:1387 assets/js/app.js:7815 assets/js/app.js:9765
#: assets/js/app.js:10710
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1627
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1678
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1957
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:2233
msgid "Next"
msgstr "Successivo"

#: includes/frontend.php:1386 assets/js/app.js:8907 assets/js/app.js:9752
#: assets/js/app.js:10703
msgid "Previous"
msgstr "Precedente"

#: includes/widgets/divider.php:340 modules/shapes/module.php:46
msgid "Line"
msgstr "Linea"

#: includes/widgets/divider.php:164
msgctxt "Shapes"
msgid "Slashes"
msgstr "Tagli"

#: includes/widgets/divider.php:148
msgctxt "Shapes"
msgid "Curved"
msgstr "Curva"

#: includes/widgets/divider.php:140
msgctxt "Shapes"
msgid "Curly"
msgstr "Riccia"

#: includes/widgets/divider.php:181
msgctxt "Shapes"
msgid "Wavy"
msgstr "Ondulata"

#: includes/widgets/divider.php:173
msgctxt "Shapes"
msgid "Squared"
msgstr "Quadrata"

#: includes/widgets/divider.php:156
msgctxt "Shapes"
msgid "Multiple"
msgstr "Multipla"

#: includes/widgets/divider.php:310
msgctxt "Shapes"
msgid "X"
msgstr "X"

#: includes/widgets/divider.php:301
msgctxt "Shapes"
msgid "Tribal"
msgstr "Tribale"

#: includes/widgets/divider.php:292
msgctxt "Shapes"
msgid "Trees"
msgstr "Alberi"

#: includes/widgets/divider.php:283
msgctxt "Shapes"
msgid "Squares"
msgstr "Piazze"

#: includes/widgets/divider.php:274
msgctxt "Shapes"
msgid "Stripes"
msgstr "Strisce"

#: includes/widgets/divider.php:265
msgctxt "Shapes"
msgid "Leaves"
msgstr "Foglie"

#: includes/widgets/divider.php:256
msgctxt "Shapes"
msgid "Half Rounds"
msgstr "Mezzi giri"

#: includes/widgets/divider.php:247
msgctxt "Shapes"
msgid "Fir Tree"
msgstr "Abete"

#: includes/widgets/divider.php:238
msgctxt "Shapes"
msgid "Dots"
msgstr "Punti"

#: includes/widgets/divider.php:229
msgctxt "Shapes"
msgid "Rectangles"
msgstr "Rettangoli"

#: includes/widgets/divider.php:221
msgctxt "Shapes"
msgid "Parallelogram"
msgstr "Parallelogramma"

#: includes/widgets/divider.php:213
msgctxt "Shapes"
msgid "Rhombus"
msgstr "Rombi"

#: includes/widgets/divider.php:197
msgctxt "Shapes"
msgid "Arrows"
msgstr "Frecce"

#: includes/controls/groups/background.php:583 includes/widgets/video.php:376
msgid "Play On Mobile"
msgstr "Riproduci su mobile"

#: includes/widgets/divider.php:205
msgctxt "Shapes"
msgid "Pluses"
msgstr "Pluses"

#: core/document-types/post.php:51
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:57
msgid "Post"
msgstr "Articolo"

#: includes/widgets/divider.php:667
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:52
#: assets/js/packages/editor-controls/editor-controls.strings.js:54
#: assets/js/packages/editor-controls/editor-controls.strings.js:58
#: assets/js/packages/editor-controls/editor-controls.strings.js:60
#: assets/js/packages/editor-controls/editor-controls.strings.js:62
#: assets/js/packages/editor-controls/editor-controls.strings.js:64
msgid "Amount"
msgstr "Quantità"

#. translators: %s: Path to .htaccess file.
#: core/debug/classes/htaccess.php:31
msgid "File Path: %s"
msgstr "Percorso file: %s"

#: includes/controls/groups/background.php:573
msgid "Play Once"
msgstr "Riproduci una volta"

#: includes/controls/icons.php:91 includes/controls/icons.php:112
#: includes/controls/icons.php:198
msgid "Upload SVG"
msgstr "Carica SVG"

#: includes/controls/media.php:192
msgid "Choose Video"
msgstr "Seleziona video"

#: includes/admin-templates/beta-tester.php:32
msgid "Your Email"
msgstr "La tua email"

#: includes/admin-templates/beta-tester.php:29
msgid "Get Beta Updates"
msgstr "Ottenere aggiornamenti beta"

#: includes/settings/settings.php:361
msgid "We recommend you only enable this feature if you understand the security risks involved."
msgstr "Si consiglia di attivare questa funzione solo se si conoscono i rischi per la sicurezza."

#: includes/template-library/sources/local.php:618
msgid "Template not exist."
msgstr "Il template non esiste."

#: core/experiments/manager.php:658 includes/base/widget-base.php:1019
msgid "Deprecated"
msgstr "Deprecato"

#: includes/managers/icons.php:551
msgid "Hurray! The upgrade process to Font Awesome 5 was completed successfully."
msgstr "Evviva! Il processo di aggiornamento a Font Awesome 5 è stato completato con successo."

#: includes/managers/icons.php:505
msgid "Upgrade To Font Awesome 5"
msgstr "Aggiorna a Font Awesome 5"

#: includes/managers/icons.php:493
msgid "This action is not reversible and cannot be undone by rolling back to previous versions."
msgstr "Questa azione non è reversibile e non può essere annullata riportandola alle versioni precedenti."

#: includes/managers/icons.php:486
msgid "By upgrading, whenever you edit a page containing a Font Awesome 4 icon, Elementor will convert it to the new Font Awesome 5 icon."
msgstr "Con l'aggiornamento, ogni volta che si modifica una pagina contenente un'icona Font Awesome 4, Elementor la convertirà nella nuova icona Font Awesome 5."

#: includes/managers/icons.php:485
msgid "Access 1,500+ amazing Font Awesome 5 icons and enjoy faster performance and design flexibility."
msgstr "Accesso a più di 1.500 fantastiche icone Font Awesome 5 icone e godere di prestazioni più veloci e flessibilità di progettazione."

#: includes/managers/icons.php:479 includes/managers/icons.php:483
#: includes/managers/icons.php:498
msgid "Font Awesome Upgrade"
msgstr "Aggiornamento Font Awesome 5"

#: includes/managers/icons.php:472
msgid "Font Awesome 4 support script (shim.js) is a script that makes sure all previously selected Font Awesome 4 icons are displayed correctly while using Font Awesome 5 library."
msgstr "Lo script di supporto Font Awesome 4 (shim.js) è un file che assicura che tutte le icone Font Awesome 4 precedentemente selezionate siano visualizzate correttamente mentre si utilizza la libreria Font Awesome 5."

#: includes/managers/icons.php:248
msgid "All Icons"
msgstr "Tutte le icone"

#: includes/managers/icons.php:156
msgid "Font Awesome - Brands"
msgstr "Font Awesome - Marchi"

#: includes/managers/icons.php:144
msgid "Font Awesome - Solid"
msgstr "Font Awesome - Solido"

#: includes/managers/icons.php:132
msgid "Font Awesome - Regular"
msgstr "Font Awesome - Regolare"

#: core/debug/classes/htaccess.php:12
msgid "Your site's .htaccess file appears to be missing."
msgstr "Il file .htaccess del tuo sito sembra mancare."

#: core/debug/classes/theme-missing.php:22
msgid "Some of your theme files are missing."
msgstr "Mancano alcuni dei tuoi file tematici."

#: includes/admin-templates/beta-tester.php:37 assets/js/beta-tester.js:64
msgid "Sign Up"
msgstr "Registrati"

#: includes/controls/media.php:283 includes/controls/media.php:285
#: assets/js/editor.js:6025
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:3
#: assets/js/packages/editor-controls/editor-controls.strings.js:40
msgid "Upload"
msgstr "Carica"

#: core/editor/editor.php:201
msgid "Document not found."
msgstr "Documento non trovato."

#: includes/admin-templates/beta-tester.php:30
msgid "As a beta tester, you’ll receive an update that includes a testing version of Elementor and its content directly to your Email"
msgstr "Come beta tester, riceverai un aggiornamento che include una versione di prova di Elementor e i suoi contenuti direttamente sulla tua email"

#: includes/managers/icons.php:464
msgid "Load Font Awesome 4 Support"
msgstr "Carica supporto Font Awesome 4"

#: includes/controls/icons.php:90 includes/controls/icons.php:116
#: includes/controls/icons.php:202 assets/js/editor.js:6606
msgid "Icon Library"
msgstr "Libreria icona"

#: includes/settings/settings.php:361
msgid "Elementor will try to sanitize the unfiltered files, removing potential malicious code and scripts."
msgstr "Elementor cercherà di sanificare i file non filtrati, rimuovendo il potenziale codice dannoso e gli script."

#: core/files/file-types/svg.php:73 core/files/uploads-manager.php:590
msgid "This file is not allowed for security reasons."
msgstr "Il File non è consentito per motivi di sicurezza."

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/elements/column.php:934 includes/elements/container.php:1893
#: includes/elements/section.php:1401 includes/widgets/common-base.php:1342
msgid "Responsive visibility will take effect only on %1$s preview mode %2$s or live page, and not while editing in Elementor."
msgstr "La visibilità responsive avrà effetto solo sulla %1$s modalità anteprima %2$s o sulla pagina live, e non durante la modifica in Elementor."

#: includes/managers/icons.php:488
msgid "Please note that the upgrade process may cause some of the previously used Font Awesome 4 icons to look a bit different due to minor design changes made by Font Awesome."
msgstr "Considera che il processo di aggiornamento può causare problemi con alcune delle icone Font Awesome 4 precedentemente utilizzate. Infatti, è possibile che abbiano un aspetto leggermente diverso a causa di piccole modifiche di design apportate da Font Awesome."

#: includes/settings/settings.php:361
msgid "Please note! Allowing uploads of any files (SVG & JSON included) is a potential security risk."
msgstr "Attenzione! Consentire il caricamento di qualsiasi file (SVG incluso) è un potenziale rischio per la sicurezza."

#: core/kits/views/panel.php:12 includes/editor-templates/panel.php:27
#: includes/editor-templates/panel.php:172
msgid "Need Help"
msgstr "Serve aiuto"

#: includes/elements/section.php:420 includes/widgets/common-base.php:514
#: includes/widgets/image-carousel.php:743
msgid "Vertical Align"
msgstr "Allineamento verticale"

#: includes/base/element-base.php:1010 includes/elements/container.php:1580
#: includes/elements/container.php:1618 includes/elements/container.php:1680
#: includes/elements/container.php:1717 includes/widgets/common-base.php:599
#: includes/widgets/common-base.php:637 includes/widgets/common-base.php:699
#: includes/widgets/common-base.php:736
#: modules/floating-buttons/base/widget-contact-button-base.php:2961
#: modules/floating-buttons/base/widget-contact-button-base.php:3015
msgid "Offset"
msgstr "Offset"

#: includes/elements/container.php:1536 includes/widgets/common-base.php:560
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:169
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:143
msgid "Fixed"
msgstr "Fisso"

#: includes/elements/container.php:1535 includes/widgets/common-base.php:559
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:142
msgid "Absolute"
msgstr "Assoluto"

#: includes/controls/groups/flex-item.php:30
#: includes/widgets/common-base.php:359
msgid "Custom Width"
msgstr "Larghezza personalizzata"

#: includes/settings/controls.php:236
msgid "Super Admin"
msgstr "Super Admin"

#: includes/elements/container.php:560 includes/elements/section.php:453
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:128
msgid "Hidden"
msgstr "Nascosto"

#: includes/elements/container.php:555 includes/elements/section.php:448
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:126
msgid "Overflow"
msgstr "Eccedenza"

#: includes/elements/column.php:869 includes/elements/container.php:1813
#: includes/elements/section.php:1310 includes/widgets/common-base.php:830
msgid "Motion Effects"
msgstr "Effetti di movimento"

#: includes/controls/groups/flex-container.php:113
#: includes/controls/groups/flex-container.php:226
#: includes/controls/groups/grid-container.php:208
#: includes/controls/groups/grid-container.php:248
#: includes/elements/column.php:192 includes/elements/column.php:220
#: includes/elements/section.php:429
msgid "Space Around"
msgstr "Spaziare Intorno"

#: includes/controls/groups/flex-container.php:117
#: includes/controls/groups/flex-container.php:230
#: includes/controls/groups/grid-container.php:212
#: includes/controls/groups/grid-container.php:252
#: includes/elements/column.php:193 includes/elements/column.php:221
#: includes/elements/section.php:430
msgid "Space Evenly"
msgstr "Spaziare Uniformemente"

#: includes/elements/container.php:1656 includes/widgets/common-base.php:675
msgid "Vertical Orientation"
msgstr "Orientamento verticale"

#: includes/elements/container.php:1555 includes/widgets/common-base.php:574
msgid "Horizontal Orientation"
msgstr "Orientamento orizzontale"

#: includes/settings/admin-menu-items/get-help-menu-item.php:23
msgid "Get Help"
msgstr "Ottieni aiuto"

#: includes/elements/container.php:1518 includes/widgets/common-base.php:543
msgid "Custom positioning is not considered best practice for responsive web design and should not be used too frequently."
msgstr "Il posizionamento personalizzato non è considerato la best practice per il responsive web design e non dovrebbe essere usato troppo frequentemente."

#: includes/elements/container.php:1517 includes/widgets/common-base.php:542
msgid "Please note!"
msgstr "Nota bene!"

#: modules/safe-mode/module.php:256
msgid "The issue was probably caused by one of your plugins or theme."
msgstr "Il problema è stato probabilmente causato da uno dei tuoi plugin o dal tema."

#. translators: %s: Accepted chars.
#: includes/widgets/menu-anchor.php:136
msgid "Note: The ID link ONLY accepts these chars: %s"
msgstr "Nota: Il collegamento ID accetta SOLO questi caratteri: %s"

#: modules/safe-mode/module.php:358
msgid "Having problems loading Elementor? Please enable Safe Mode to troubleshoot."
msgstr "Hai problemi a caricare Elementor? Abilita la Modalità sicura per la risoluzione dei problemi."

#: includes/template-library/sources/local.php:1733
#: modules/promotions/admin-menu-items/popups-promotion-item.php:41
#: modules/promotions/admin-menu-items/popups-promotion-item.php:45
#: assets/js/app.js:6336 assets/js/app.js:11343
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:2649
msgid "Popups"
msgstr "Popup"

#: includes/template-library/sources/local.php:322
msgctxt "Template Library"
msgid "Category"
msgstr "Categoria"

#: includes/template-library/sources/local.php:321
msgctxt "Template Library"
msgid "Categories"
msgstr "Categorie"

#: includes/widgets/read-more.php:95
msgid "Continue reading"
msgstr "Continua a leggere"

#: includes/widgets/read-more.php:40 includes/widgets/read-more.php:91
msgid "Read More"
msgstr "Leggi tutto"

#. translators: %d: Interval in minutes.
#: core/base/background-process/wp-background-process.php:439
#: core/base/background-task.php:316
msgid "Every %d minutes"
msgstr "Ogni %d minuti"

#. translators: %s: Current post name.
#: includes/frontend.php:1522
msgid "Continue reading %s"
msgstr "Continua a leggere %s"

#: includes/widgets/video.php:245
msgid "External URL"
msgstr "URL esterno"

#: includes/widgets/image-gallery.php:263
msgid "Order By"
msgstr "Ordina per"

#: includes/widgets/read-more.php:124
msgid "Read More Text"
msgstr "Testo Leggi Tutto"

#: includes/widgets/google-maps.php:154
#: modules/floating-buttons/base/widget-contact-button-base.php:396
#: modules/floating-buttons/base/widget-contact-button-base.php:958
#: modules/link-in-bio/base/widget-link-in-bio-base.php:502
#: modules/link-in-bio/base/widget-link-in-bio-base.php:753
msgid "Location"
msgstr "Posizione"

#: includes/template-library/sources/local.php:1455
msgctxt "Template Library"
msgid "Filter by category"
msgstr "Filtra per categoria"

#: core/admin/menu/main.php:34 core/admin/menu/main.php:35
#: includes/template-library/sources/local.php:239
msgctxt "Template Library"
msgid "Templates"
msgstr "Template"

#: modules/promotions/admin-menu-items/popups-promotion-item.php:18
msgid "Get Popup Builder"
msgstr "Ottieni Popup Builder"

#: app/admin-menu-items/theme-builder-menu-item.php:22
#: app/modules/site-editor/module.php:31
#: core/common/modules/finder/categories/general.php:72
#: includes/template-library/sources/local.php:1732
#: assets/js/app-packages.js:4613 assets/js/editor.js:47647
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:7
msgid "Theme Builder"
msgstr "Theme Builder"

#: modules/safe-mode/module.php:352 modules/safe-mode/module.php:364
msgid "Can't Edit?"
msgstr "Non è possibile modificare?"

#: includes/frontend.php:1515
msgid "(more&hellip;)"
msgstr "(altro&hellip;)"

#: core/upgrade/manager.php:51
msgid "Elementor Data Updater"
msgstr "Aggiornamento Dati Elementor"

#: modules/safe-mode/module.php:42
msgid "Safe Mode"
msgstr "Modalità sicura"

#: modules/safe-mode/module.php:253
msgid "Editor successfully loaded?"
msgstr "Editor caricato con successo?"

#: modules/safe-mode/module.php:269
msgid "Still experiencing issues?"
msgstr "Hai ancora problemi?"

#. translators: %s: The `the_content` function.
#: includes/widgets/read-more.php:115
msgid "Note: This widget only affects themes that use `%s` in archive pages."
msgstr "Nota: questo widget interessa solo i temi che usano `%s` nelle pagine archivio."

#: includes/controls/media.php:198
#: modules/link-in-bio/base/widget-link-in-bio-base.php:389
msgid "Choose File"
msgstr "Scegli file"

#: includes/template-library/sources/local.php:323
msgctxt "Template Library"
msgid "All Categories"
msgstr "Tutte le categorie"

#: core/base/db-upgrades-manager.php:130
msgid "The database update process is now complete. Thank you for updating to the latest version!"
msgstr "Il processo di aggiornamento del database è ora completo. Grazie per l'aggiornamento all'ultima versione!"

#: core/base/db-upgrades-manager.php:93
msgid "Your site database needs to be updated to the latest version."
msgstr "Il database del tuo sito deve essere aggiornato alla versione più recente."

#: modules/promotions/admin-menu-items/popups-promotion-item.php:19
msgid "The Popup Builder lets you take advantage of all the amazing features in Elementor, so you can build beautiful & highly converting popups. Get Elementor Pro and start designing your popups today."
msgstr "Popup Builder ti consente di sfruttare tutte le fantastiche funzionalità di Elementor, in modo da poter creare popup bellissimi e altamente convertitori. Ottieni Elementor Pro e inizia a progettare i tuoi popup oggi."

#: modules/safe-mode/module.php:354
msgid "Enable Safe Mode"
msgstr "Abilita la modalità sicura"

#: modules/safe-mode/module.php:246 modules/safe-mode/module.php:476
msgid "Disable Safe Mode"
msgstr "Disabilita la modalità sicura"

#: modules/safe-mode/module.php:244
msgid "Safe Mode ON"
msgstr "Modalità sicura attivata"

#: modules/safe-mode/module.php:102
msgid "Cannot enable Safe Mode"
msgstr "Impossibile abilitare la modalità sicura"

#: modules/safe-mode/module.php:51
msgid "Safe Mode allows you to troubleshoot issues by only loading the editor, without loading the theme or any other plugin."
msgstr "La modalità sicura ti consente di risolvere i problemi caricando solo l'editor, senza caricare il tema o altri plugin."

#: modules/library/documents/not-supported.php:56
msgid "Not Supported"
msgstr "Non supportato"

#: core/common/modules/finder/categories/site.php:82
msgid "Users"
msgstr "Utenti"

#: core/common/modules/finder/categories/site.php:76 assets/js/app.js:4618
#: assets/js/app.js:6350 assets/js/app.js:11863
msgid "Plugins"
msgstr "Plugin"

#: core/common/modules/connect/apps/base-app.php:87
msgid "Disconnect"
msgstr "Disconnetti"

#: core/common/modules/connect/apps/connect.php:11
#: core/common/modules/connect/connect-menu-item.php:24
#: core/common/modules/connect/connect-menu-item.php:28
#: core/utils/hints.php:461 includes/editor-templates/templates.php:521
#: modules/cloud-library/module.php:144 assets/js/ai-admin.js:1069
#: assets/js/ai-admin.js:6385 assets/js/ai-gutenberg.js:1207
#: assets/js/ai-gutenberg.js:6603 assets/js/ai-layout.js:767
#: assets/js/ai-layout.js:2494 assets/js/ai-media-library.js:1069
#: assets/js/ai-media-library.js:6385 assets/js/ai-unify-product-images.js:1069
#: assets/js/ai-unify-product-images.js:6385 assets/js/ai.js:1852
#: assets/js/ai.js:7755 assets/js/ai.js:7846 assets/js/editor.js:9818
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3256
msgid "Connect"
msgstr "Connetti"

#: includes/widgets/star-rating.php:318
msgid "Stars"
msgstr "Stelle"

#: core/common/modules/connect/apps/base-app.php:232
#: core/common/modules/connect/rest/rest-api.php:117
msgid "Disconnected successfully."
msgstr "Disconnesso con successo."

#: core/common/modules/connect/apps/base-app.php:220
#: core/common/modules/connect/rest/rest-api.php:87 assets/js/editor.js:9868
#: assets/js/editor.js:9933 assets/js/editor.js:10915
msgid "Connected successfully."
msgstr "Connesso con successo."

#: includes/editor-templates/hotkeys.php:160
msgid "Go To"
msgstr "Vai a"

#: core/common/modules/finder/categories/site.php:58
msgid "Menus"
msgstr "Menu"

#: core/common/modules/finder/categories/site.php:52
msgid "Dashboard"
msgstr "Bacheca"

#: core/common/modules/finder/categories/site.php:46
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:12
msgid "Homepage"
msgstr "Homepage"

#: core/common/modules/finder/categories/site.php:70
msgid "Customizer"
msgstr "Personalizza"

#: includes/widgets/rating.php:147 includes/widgets/star-rating.php:140
msgid "Rating Scale"
msgstr "Scala di valutazione"

#: includes/widgets/image.php:163 includes/widgets/image.php:175
msgid "Custom Caption"
msgstr "Didascalia personalizzata"

#: includes/editor-templates/hotkeys.php:201
msgid "Quit"
msgstr "Smettere"

#: includes/editor-templates/hotkeys.php:193 assets/js/editor.js:5336
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:16
msgid "Keyboard Shortcuts"
msgstr "Tasti rapidi"

#: includes/editor-templates/hotkeys.php:104
msgid "Show / Hide Panel"
msgstr "Mostra / nascondi pannello"

#: includes/editor-templates/hotkeys.php:21 assets/js/ai-admin.js:12569
#: assets/js/ai-admin.js:13655 assets/js/ai-gutenberg.js:12787
#: assets/js/ai-gutenberg.js:13873 assets/js/ai-media-library.js:12569
#: assets/js/ai-media-library.js:13655
#: assets/js/ai-unify-product-images.js:12569
#: assets/js/ai-unify-product-images.js:13655 assets/js/ai.js:14030
#: assets/js/ai.js:15116 assets/js/editor.js:9472
#: assets/js/kit-elements-defaults-editor.js:232
msgid "Undo"
msgstr "Annulla"

#: includes/editor-templates/hotkeys.php:29 assets/js/ai-admin.js:12580
#: assets/js/ai-admin.js:13666 assets/js/ai-gutenberg.js:12798
#: assets/js/ai-gutenberg.js:13884 assets/js/ai-media-library.js:12580
#: assets/js/ai-media-library.js:13666
#: assets/js/ai-unify-product-images.js:12580
#: assets/js/ai-unify-product-images.js:13666 assets/js/ai.js:14041
#: assets/js/ai.js:15127
msgid "Redo"
msgstr "Ripeti"

#: core/common/modules/finder/template.php:13
msgid "Type to find anything in Elementor"
msgstr "Digita per trovare qualcosa in Elementor"

#: includes/widgets/video.php:517
msgid "Any Video"
msgstr "Qualsiasi video"

#: includes/widgets/video.php:516
msgid "Current Video Channel"
msgstr "Canale video corrente"

#: includes/widgets/star-rating.php:192
msgid "Outline"
msgstr "Contorno"

#: includes/widgets/star-rating.php:184
msgid "Unmarked Style"
msgstr "Stile non marcato"

#: includes/widgets/rating.php:22 includes/widgets/rating.php:140
#: includes/widgets/rating.php:164 includes/widgets/star-rating.php:153
msgid "Rating"
msgstr "Valutazione"

#: includes/widgets/star-rating.php:45 includes/widgets/star-rating.php:122
msgid "Star Rating"
msgstr "Stelle"

#: core/base/document.php:1996
msgid "Future"
msgstr "Futuro"

#: includes/widgets/rating.php:122 includes/widgets/star-rating.php:388
msgid "Unmarked Color"
msgstr "Colore non marcato"

#: includes/editor-templates/hotkeys.php:96 assets/js/admin-top-bar.js:187
#: assets/js/common.js:2566 assets/js/editor.js:38604
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:18
msgid "Finder"
msgstr "Finder"

#: includes/widgets/image-gallery.php:175 includes/widgets/image.php:162
msgid "Attachment Caption"
msgstr "Didascalia allegato"

#: core/common/modules/finder/categories/create.php:27 assets/js/editor.js:8874
#: assets/js/editor.js:46245
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:24
#: assets/js/packages/editor-variables/editor-variables.strings.js:36
msgid "Create"
msgstr "Crea"

#: includes/widgets/video.php:619
msgid "Poster"
msgstr "Poster"

#: core/admin/admin-notices.php:318
msgid "Congrats!"
msgstr "Congratulazioni!"

#: core/admin/admin-notices.php:322
msgid "Happy To Help"
msgstr "Felice di aiutare"

#: includes/editor-templates/navigator.php:96
msgid "Once you fill your page with content, this window will give you an overview display of all the page elements. This way, you can easily move around any section, column, or widget."
msgstr "Una volta che hai riempito la tua pagina di contenuti, questa finestra ti mostrerà una panoramica di tutti gli elementi della pagina. In questo modo, puoi spostare facilmente le diverse sezioni, colonne e widget."

#: core/admin/admin-notices.php:328
msgid "Hide Notification"
msgstr "Nascondi notifica"

#: includes/widgets/inner-section.php:35 assets/js/editor.js:28004
msgid "Inner Section"
msgstr "Sezione Interna"

#: includes/widgets/accordion.php:171 includes/widgets/accordion.php:175
#: includes/widgets/icon-box.php:186 includes/widgets/image-box.php:161
#: includes/widgets/tabs.php:170 includes/widgets/tabs.php:174
#: includes/widgets/testimonial.php:145 includes/widgets/text-editor.php:142
#: includes/widgets/toggle.php:174 includes/widgets/toggle.php:178
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."
msgstr "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."

#: includes/settings/admin-menu-items/getting-started-menu-item.php:61
msgid "Welcome to Elementor"
msgstr "Benvenuto su Elementor"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:23
#: includes/settings/admin-menu-items/getting-started-menu-item.php:27
#: includes/settings/admin-menu-items/getting-started-menu-item.php:52
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:744
msgid "Getting Started"
msgstr "Iniziare"

#: includes/editor-templates/navigator.php:95
msgid "Easy Navigation is Here!"
msgstr "La navigazione semplice è qui!"

#: includes/editor-templates/navigator.php:90
msgid "Empty"
msgstr "Vuoto"

#: includes/editor-templates/library-layout.php:13
#: includes/settings/admin-menu-items/getting-started-menu-item.php:56
#: modules/announcements/module.php:124 assets/js/app-packages.js:1359
#: assets/js/app.js:1718
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1214
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1432
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1524
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1775
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1948
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:2268
msgid "Skip"
msgstr "Salta"

#: core/debug/inspector.php:49
msgid "Debug Bar"
msgstr "Barra Debug"

#: includes/widgets/video.php:479
msgid "Lazy Load"
msgstr "Lazy Load"

#: includes/widgets/video.php:181 includes/widgets/video.php:206
#: includes/widgets/video.php:230 includes/widgets/video.php:290
msgid "Enter your URL"
msgstr "Inserisci il tuo URL"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:39
msgid "Create Your First Post"
msgstr "Crea il tuo primo articolo"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:36
msgid "Create Your First Page"
msgstr "Crea la tua prima pagina"

#: includes/controls/groups/css-filter.php:129
msgctxt "Filter Control"
msgid "Hue"
msgstr "Tonalità"

#: modules/floating-buttons/base/widget-contact-button-base.php:951
#: modules/floating-buttons/base/widget-floating-bars-base.php:166
#: modules/floating-buttons/base/widget-floating-bars-base.php:352
#: modules/link-in-bio/base/widget-link-in-bio-base.php:271
#: modules/shapes/widgets/text-path.php:164
msgid "Paste URL or type"
msgstr "Incolla l’URL o digita"

#: core/admin/admin-notices.php:319
msgid "You created over 10 pages with Elementor. Great job! If you can spare a minute, please help us by leaving a five star review on WordPress.org."
msgstr "Hai creato più di 10 pagine con Elementor. Ottimo lavoro! Se puoi risparmiare un minuto, per favore aiutaci lasciando una recensione a cinque stelle su WordPress.org."

#: core/kits/documents/tabs/settings-lightbox.php:76 includes/frontend.php:1383
#: includes/widgets/google-maps.php:174 assets/js/ai-admin.js:10213
#: assets/js/ai-admin.js:10216 assets/js/ai-gutenberg.js:10431
#: assets/js/ai-gutenberg.js:10434 assets/js/ai-media-library.js:10213
#: assets/js/ai-media-library.js:10216
#: assets/js/ai-unify-product-images.js:10213
#: assets/js/ai-unify-product-images.js:10216 assets/js/ai.js:11674
#: assets/js/ai.js:11677
msgid "Zoom"
msgstr "Zoom"

#: core/debug/inspector.php:57
msgid "Debug Bar adds an admin bar menu that lists all the templates that are used on a page that is being displayed."
msgstr "La Barra di Debug aggiunge un menu nella barra di amministrazione che elenca tutti i template utilizzati in una pagina che viene visualizzata."

#: core/document-types/page-base.php:47
msgid "Single"
msgstr "Singolo"

#: includes/widgets/video.php:451
msgid "Logo"
msgstr "Logo"

#: includes/widgets/video.php:424
msgid "Video Info"
msgstr "Info Video"

#: core/base/providers/social-network-provider.php:216
#: includes/widgets/video.php:277 includes/widgets/video.php:301
msgid "URL"
msgstr "URL"

#: includes/widgets/video.php:163
msgid "Self Hosted"
msgstr "Auto Ospitato"

#: includes/widgets/video.php:161
msgid "Dailymotion"
msgstr "Dailymotion"

#: includes/widgets/video.php:155
msgid "Source"
msgstr "Sorgente"

#: includes/widgets/audio.php:193
msgid "Artwork"
msgstr "Opera d'arte"

#: includes/managers/elements.php:317
msgid "WooCommerce"
msgstr "WooCommerce"

#: app/modules/site-editor/module.php:32
#: core/common/modules/finder/categories/site.php:26 core/kits/manager.php:437
#: includes/managers/elements.php:310 modules/admin-bar/module.php:149
msgid "Site"
msgstr "Sito"

#: includes/editor-templates/templates.php:122
#: includes/editor-templates/templates.php:640
#: includes/managers/elements.php:292
#: modules/promotions/widgets/pro-widget-promotion.php:67
#: assets/js/ai-admin.js:7980 assets/js/ai-gutenberg.js:8198
#: assets/js/ai-layout.js:3471 assets/js/ai-media-library.js:7980
#: assets/js/ai-unify-product-images.js:7980 assets/js/ai.js:9441
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3704
msgid "Pro"
msgstr "Pro"

#: includes/elements/column.php:447 includes/elements/container.php:856
#: includes/elements/section.php:715 includes/widgets/heading.php:324
msgid "Blend Mode"
msgstr "Modalità di fusione"

#: includes/editor-templates/hotkeys.php:38
#: includes/editor-templates/templates.php:165 assets/js/editor.js:8572
#: assets/js/editor.js:8586 assets/js/editor.js:30658
msgid "Copy"
msgstr "Copia"

#: includes/editor-templates/global.php:49
#: assets/js/152f977e0c1304a3b0db.bundle.js:128
msgid "Drag widget here"
msgstr "Trascina il widget qui"

#: includes/controls/groups/css-filter.php:113
msgctxt "Filter Control"
msgid "Saturation"
msgstr "Saturazione"

#: includes/controls/groups/css-filter.php:97
msgctxt "Filter Control"
msgid "Contrast"
msgstr "Contrasto"

#: includes/controls/groups/css-filter.php:81
msgctxt "Filter Control"
msgid "Brightness"
msgstr "Luminosità"

#: includes/controls/groups/css-filter.php:62
msgctxt "Filter Control"
msgid "Blur"
msgstr "Sfocatura"

#: includes/controls/groups/background.php:561 includes/widgets/video.php:339
msgid "End Time"
msgstr "Orario di fine"

#: includes/controls/groups/background.php:549 includes/widgets/video.php:328
msgid "Start Time"
msgstr "Orario di inizio"

#: core/admin/feedback.php:107
msgid "Wait! Don't deactivate Elementor. You have to activate both Elementor and Elementor Pro in order for the plugin to work."
msgstr "Aspetta! Non disattivare Elementor. Devi attivare sia Elementor che Elementor Pro affinché il plugin funzioni."

#: core/admin/feedback.php:105
msgid "I have Elementor Pro"
msgstr "Ho Elementor Pro"

#: core/debug/inspector.php:115
msgid "Elementor Debugger"
msgstr "Debugger Elementor"

#: includes/widgets/traits/button-trait.php:205
msgid "Button ID"
msgstr "ID pulsante"

#: includes/controls/groups/background.php:563 includes/widgets/video.php:341
msgid "Specify an end time (in seconds)"
msgstr "Specifica un orario di fine (in secondi)"

#: includes/controls/groups/background.php:551 includes/widgets/video.php:330
msgid "Specify a start time (in seconds)"
msgstr "Specifica un orario di inizio (in secondi)"

#. translators: 1: `<code>` opening tag, 2: `</code>` closing tag.
#: includes/widgets/traits/button-trait.php:217
msgid "Please make sure the ID is unique and not used elsewhere on the page. This field allows %1$sA-z 0-9%2$s & underscore chars without spaces."
msgstr "Assicurati che l'ID sia univoco e non venga utilizzato altrove nella pagina in cui viene visualizzato il modulo. Questo campo consente %1$sA-z 0-9%2$s e trattini bassi senza spazi."

#: core/admin/admin.php:219 assets/js/admin.js:2097 assets/js/gutenberg.js:147
msgid "Back to WordPress Editor"
msgstr "Torna all'editor di WordPress"

#. translators: %s: Document title.
#: core/documents-manager.php:388
msgid "Elementor %s"
msgstr "Elementor %s"

#. translators: %s: Document title.
#. translators: %s: Template type label.
#: core/base/document.php:269
#: core/common/modules/finder/categories/create.php:86
#: core/document-types/page-base.php:183
#: includes/template-library/sources/local.php:1416
msgid "Add New %s"
msgstr "Aggiungi nuovo %s"

#: core/kits/documents/tabs/theme-style-images.php:95
#: core/kits/documents/tabs/theme-style-images.php:166
#: includes/elements/column.php:416 includes/elements/column.php:490
#: includes/elements/container.php:810 includes/elements/container.php:924
#: includes/elements/section.php:669 includes/elements/section.php:773
#: includes/widgets/image-box.php:504 includes/widgets/image-box.php:539
#: includes/widgets/image.php:441 includes/widgets/image.php:475
#: modules/floating-buttons/base/widget-floating-bars-base.php:1010
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1502
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:212
msgid "Opacity"
msgstr "Opacità"

#: includes/widgets/image.php:319
msgid "Max Width"
msgstr "Larghezza Massima"

#: includes/controls/groups/background.php:265
#: includes/controls/groups/background.php:313
#: includes/controls/groups/box-shadow.php:69
#: includes/elements/container.php:1530 includes/widgets/common-base.php:554
#: includes/widgets/common-base.php:1203 includes/widgets/divider.php:766
#: includes/widgets/divider.php:932 includes/widgets/image-carousel.php:583
#: includes/widgets/image-carousel.php:647 includes/widgets/tabs.php:184
#: includes/widgets/traits/button-trait.php:252
#: modules/floating-buttons/base/widget-floating-bars-base.php:448
#: modules/link-in-bio/base/widget-link-in-bio-base.php:943
#: modules/link-in-bio/base/widget-link-in-bio-base.php:998
#: modules/nested-accordion/widgets/nested-accordion.php:213
#: modules/nested-tabs/widgets/nested-tabs.php:857
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:73
#: assets/js/packages/editor-controls/editor-controls.strings.js:168
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:12
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:137
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:139
msgid "Position"
msgstr "Posizione"

#: includes/template-library/sources/local.php:232
#: assets/js/new-template.js:147
msgid "New Template"
msgstr "Nuovo template"

#. translators: 1: Elementor, 2: Link to plugin review
#: core/admin/admin.php:408
msgid "Enjoyed %1$s? Please leave us a %2$s rating. We really appreciate your support!"
msgstr "Ti è piaciuto %1$s? Per favore lasciaci una valutazione di %2$s. Apprezziamo molto il tuo supporto!"

#: core/common/modules/finder/categories/general.php:67
msgid "Knowledge Base"
msgstr "Documentazione"

#: modules/page-templates/module.php:352
msgid "This template includes the header, full-width content and footer"
msgstr "Questo template include l'header, il contenuto a larghezza intera ed il footer"

#: modules/page-templates/module.php:340
msgid "No header, no footer, just Elementor"
msgstr "Nessun header, nessun footer, solo Elementor"

#: modules/page-templates/module.php:328
msgid "Default Page Template from your theme."
msgstr "Template di pagina predefinito dal tema"

#: modules/page-templates/module.php:297
msgid "Page Layout"
msgstr "Layout della pagina"

#: includes/widgets/common-base.php:343 includes/widgets/icon-list.php:126
#: includes/widgets/icon-list.php:217
msgid "Inline"
msgstr "In linea"

#: includes/widgets/counter.php:210
msgid "Separator"
msgstr "Separatore"

#: core/document-types/page-base.php:182
#: includes/template-library/sources/admin-menu-items/add-new-template-menu-item.php:23
#: modules/landing-pages/module.php:296 assets/js/app-packages.js:3211
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:8
msgid "Add New"
msgstr "Aggiungi nuovo"

#: includes/admin-templates/new-floating-elements.php:47
#: includes/admin-templates/new-template.php:112
msgid "Enter template name (optional)"
msgstr "Inserisci nome template (opzionale)"

#: core/base/document.php:259
msgid "Document"
msgstr "Documento"

#: core/common/modules/ajax/module.php:165
msgid "Action not found."
msgstr "Azione non trovata."

#: core/common/modules/ajax/module.php:131
msgid "Token Expired."
msgstr "Token scaduto."

#: core/document-types/page.php:72 modules/library/documents/page.php:61
#: assets/js/app.js:12162 assets/js/editor.js:7963
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:2621
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:1
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:3
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:7
msgid "Pages"
msgstr "Pagine"

#: includes/editor-templates/global.php:120
msgid "This tag has no settings."
msgstr "Questo tag non ha impostazioni."

#: core/role-manager/role-manager.php:239
msgid "Want to give access only to content?"
msgstr "Vuoi dare accesso solo ai contenuti?"

#: core/role-manager/role-manager.php:157
msgid "No access to editor"
msgstr "Nessun accesso all'editor"

#: core/dynamic-tags/tag.php:115 includes/settings/settings.php:389
msgid "Fallback"
msgstr "Fallback"

#: core/document-types/page-base.php:124
msgid "Body Style"
msgstr "Stile Body"

#: includes/admin-templates/new-template.php:66
msgid "Select the type of template you want to work on"
msgstr "Seleziona il tipo di template su cui desideri lavorare"

#: includes/admin-templates/new-template.php:57
msgid "Use templates to create the different pieces of your site, and reuse them with one click whenever needed."
msgstr "Usa i template per creare i diversi pezzi del tuo sito e riutilizzali con un clic quando necessario."

#: includes/editor-templates/templates.php:310
#: includes/editor-templates/templates.php:355
#: includes/editor-templates/templates.php:402
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:50
msgid "More actions"
msgstr "Più azioni"

#: core/kits/documents/tabs/global-typography.php:182
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:19
#: assets/js/app.js:6344
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:248
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:235
msgid "Custom Fonts"
msgstr "Font Personalizzati"

#: core/role-manager/role-manager.php:150
msgid "Role Excluded"
msgstr "Ruolo Escluso"

#: core/role-manager/role-manager.php:114
msgid "Manage What Your Users Can Edit In Elementor"
msgstr "Gestisci ciò che i tuoi utenti possono modificare in Elementor"

#: core/document-types/page-base.php:230
msgid "Featured Image"
msgstr "Immagine in evidenza"

#: includes/template-library/sources/local.php:1376
msgid "Add templates and reuse them across your website. Easily export and import them to any other project, for an optimized workflow."
msgstr "Aggiungi template e riutilizzali sul tuo sito web. Esportali e importali facilmente in qualsiasi altro progetto, per un flusso di lavoro ottimizzato."

#: includes/template-library/sources/local.php:227
msgctxt "Template Library"
msgid "My Templates"
msgstr "I miei template"

#: includes/admin-templates/new-template.php:115
msgid "Create Template"
msgstr "Crea template"

#: includes/editor-templates/templates.php:152
msgid "Search Templates:"
msgstr "Cerca template:"

#: includes/admin-templates/new-template.php:64
msgid "Choose Template Type"
msgstr "Scegli il tipo di template"

#: includes/frontend.php:1385 includes/widgets/video.php:999
msgid "Play Video"
msgstr "Riproduci video"

#: includes/template-library/sources/local.php:1281
msgid "All"
msgstr "Tutti"

#. translators: %s: Document title.
#. translators: %s: Post type label.
#: core/base/document.php:1231 core/settings/page/model.php:127
#: includes/editor-templates/panel.php:77
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:32
msgid "%s Settings"
msgstr "Impostazioni di %s"

#: includes/admin-templates/new-floating-elements.php:44
#: includes/admin-templates/new-template.php:109
msgid "Name your template"
msgstr "Nome del tuo template"

#. translators: %s: Template type label.
#: includes/template-library/sources/local.php:1409
msgid "Create Your First %s"
msgstr "Crea il tuo primo contenuto %s"

#: core/common/modules/finder/categories/general.php:61
#: core/role-manager/role-manager-menu-item.php:28
#: core/role-manager/role-manager-menu-item.php:32
#: core/role-manager/role-manager.php:50
msgid "Role Manager"
msgstr "Role Manager"

#: includes/widgets/image-carousel.php:204
msgid "Set how many slides are scrolled per swipe."
msgstr "Imposta il numero di slide che scorrono per ogni scorrimento."

#: core/admin/admin.php:477
msgid "Create New Post"
msgstr "Crea nuovo articolo"

#: includes/controls/groups/background.php:448
msgid "Note: Attachment Fixed works only on desktop."
msgstr "Nota: L'attaccamento fisso lavora solo sui desktop."

#: includes/fonts.php:77
msgid "Google (Early Access)"
msgstr "Google (Early Access)"

#: includes/controls/groups/typography.php:185
msgctxt "Typography Control"
msgid "Decoration"
msgstr "Decorazione"

#: includes/widgets/shortcode.php:110
msgid "Enter your shortcode"
msgstr "Inserisci il tuo shortcode"

#: includes/widgets/html.php:107
msgid "Enter your code"
msgstr "Inserisci il tuo codice"

#: includes/widgets/alert.php:150 includes/widgets/icon-box.php:187
#: includes/widgets/image-box.php:162
msgid "Enter your description"
msgstr "Inserisci la tua descrizione"

#: includes/widgets/alert.php:137
msgid "This is an Alert"
msgstr "Questo è un avviso"

#: includes/widgets/accordion.php:213 includes/widgets/toggle.php:216
#: modules/nested-tabs/widgets/nested-tabs.php:137
msgid "Active Icon"
msgstr "Icona attiva"

#. translators: 1: Editing date, 2: Author display name.
#: core/base/document.php:1555
msgid "Last edited on %1$s by %2$s"
msgstr "Ultima modifica su %1$s da %2$s"

#. translators: 1: Saving date, 2: Author display name.
#: core/base/document.php:1548
msgid "Draft saved on %1$s by %2$s"
msgstr "Bozza salvata su %1$s da %2$s"

#: core/base/document.php:1542
msgctxt "revision date format"
msgid "M j, H:i"
msgstr "M j, H:i"

#: core/kits/documents/kit.php:155
#: modules/history/views/revisions-panel-template.php:64
msgid "Published"
msgstr "Pubblicato"

#: includes/editor-templates/templates.php:565
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:2120
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:2462
msgid "or"
msgstr "o"

#: includes/editor-templates/templates.php:564
msgid "Drag & drop your .JSON or .zip template file"
msgstr "Trascina e rilascia il file .JSON o .zip"

#: includes/editor-templates/templates.php:256
msgid "Favorite"
msgstr "Preferito"

#: includes/editor-templates/templates.php:153
#: assets/js/packages/editor-controls/editor-controls.js:28
#: assets/js/packages/editor-controls/editor-controls.strings.js:89
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:21
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:14
#: assets/js/packages/editor-variables/editor-variables.strings.js:26
msgid "Search"
msgstr "Cerca"

#: includes/editor-templates/templates.php:81
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4742
msgid "Popular"
msgstr "Popolare"

#: includes/editor-templates/templates.php:79
msgid "Trend"
msgstr "Tendenza"

#: includes/editor-templates/templates.php:77
#: includes/editor-templates/templates.php:121
#: includes/editor-templates/templates.php:639
#: assets/js/atomic-widgets-editor.js:801 assets/js/editor.js:33491
#: assets/js/editor.js:33989 assets/js/editor.js:48583
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4738
msgid "New"
msgstr "Nuovo"

#: core/kits/views/panel.php:40 includes/controls/icons.php:83
#: includes/controls/icons.php:85 includes/controls/media.php:215
#: includes/controls/media.php:217 includes/controls/media.php:279
#: includes/controls/media.php:281 includes/editor-templates/repeater.php:23
#: modules/promotions/widgets/pro-widget-promotion.php:75
#: assets/js/ai-admin.js:2305 assets/js/ai-admin.js:7471
#: assets/js/ai-gutenberg.js:2443 assets/js/ai-gutenberg.js:7689
#: assets/js/ai-layout.js:2962 assets/js/ai-media-library.js:2305
#: assets/js/ai-media-library.js:7471 assets/js/ai-unify-product-images.js:2305
#: assets/js/ai-unify-product-images.js:7471 assets/js/ai.js:3088
#: assets/js/ai.js:8932
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:748
#: assets/js/packages/editor-controls/editor-controls.js:28
#: assets/js/packages/editor-controls/editor-controls.strings.js:86
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:17
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:68
msgid "Remove"
msgstr "Rimuovi"

#: includes/editor-templates/hotkeys.php:70
#: includes/editor-templates/repeater.php:18 assets/js/editor.js:30643
#: assets/js/editor.js:52113
#: assets/js/packages/editor-controls/editor-controls.js:28
#: assets/js/packages/editor-controls/editor-controls.strings.js:83
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:20
msgid "Duplicate"
msgstr "Duplica"

#: includes/editor-templates/panel.php:140
#: includes/editor-templates/panel.php:142 assets/js/editor.js:36900
msgid "Hide Panel"
msgstr "Nascondi pannello"

#: includes/editor-templates/panel.php:126
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:23
msgid "Save as Template"
msgstr "Salva come template"

#: core/base/document.php:173 includes/editor-templates/panel.php:103
#: assets/js/editor.js:25670
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:28
msgid "Publish"
msgstr "Pubblica"

#: includes/editor-templates/panel-elements.php:74
msgid "Search Widget:"
msgstr "Cerca widget:"

#: includes/controls/dimensions.php:148
msgid "Unlinked values"
msgstr "Valori non collegati"

#: core/admin/admin.php:617
msgid "Blog"
msgstr "Blog"

#: core/admin/admin.php:597
msgid "(opens in a new window)"
msgstr "(apri in una nuova scheda)"

#: core/admin/admin.php:532
msgctxt "Dashboard Overview Widget Recently Date"
msgid "M jS"
msgstr "M jS"

#: core/admin/admin.php:428
msgid "Elementor Overview"
msgstr "Panoramica di Elementor"

#: includes/controls/groups/typography.php:191
msgctxt "Typography Control"
msgid "Overline"
msgstr "Sopralineato"

#: includes/controls/groups/typography.php:192
msgctxt "Typography Control"
msgid "Line Through"
msgstr "Linea in mezzo"

#. translators: %s: Document title.
#: core/base/document.php:200
msgid "Hurray! Your %s is live."
msgstr "Evviva! %s è pubblicato."

#: includes/controls/groups/background.php:597 includes/widgets/video.php:466
msgid "Privacy Mode"
msgstr "Modalità Privacy"

#: includes/widgets/image.php:178
msgid "Enter your image caption"
msgstr "Inserisci didascalia dell'immagine"

#: includes/editor-templates/templates.php:566 assets/js/app-packages.js:1031
#: assets/js/app.js:1390
msgid "Select File"
msgstr "Seleziona File"

#: includes/editor-templates/repeater.php:12
msgid "Drag & Drop"
msgstr "Trascina e Rilascia"

#: core/admin/admin.php:565
msgid "News & Updates"
msgstr "News e Aggiornamenti"

#: core/experiments/manager.php:541 includes/controls/popover-toggle.php:71
#: includes/controls/popover-toggle.php:73
msgid "Back to default"
msgstr "Torna al predefinito"

#: includes/editor-templates/templates.php:204
msgid "Created By"
msgstr "Creato da"

#: core/document-types/page-base.php:215
msgid "Excerpt"
msgstr "Riassunto"

#: includes/editor-templates/panel.php:122
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:24
msgid "Save Draft"
msgstr "Salva bozza"

#: modules/history/revisions-manager.php:157
msgid "Current Version"
msgstr "Versione corrente"

#: core/common/modules/finder/template.php:19 assets/js/editor.js:10979
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:2086
msgid "No Results Found"
msgstr "Nessun risultato trovato"

#: includes/widgets/traits/button-trait.php:57
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:50
#: assets/js/app.js:8781
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:2474
msgid "Click here"
msgstr "Fai clic qui"

#: includes/editor-templates/templates.php:563
msgid "Import Template to Your Library"
msgstr "Importa il template nella tua libreria"

#: includes/editor-templates/templates.php:208
msgid "Creation Date"
msgstr "Data di creazione"

#: includes/editor-templates/templates.php:103
msgid "My Favorites"
msgstr "I miei preferiti"

#: includes/editor-templates/templates.php:14
msgid "Import Template"
msgstr "Importa template"

#: includes/editor-templates/panel.php:91
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:30
msgid "Preview Changes"
msgstr "Anteprima modifiche"

#: includes/editor-templates/panel.php:108
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:26
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:27
msgid "Save Options"
msgstr "Salva opzioni"

#: core/admin/admin.php:524
msgid "Recently Edited"
msgstr "Modificato di recente"

#: core/admin/admin.php:474
msgid "Create New Page"
msgstr "Crea nuova pagina"

#: includes/widgets/video.php:468
msgid "When you turn on privacy mode, YouTube/Vimeo won't store information about visitors on your website unless they play the video."
msgstr "Quando attivi la modalità di privacy, YouTube/Vimeo non memorizzeranno le informazioni sui visitatori sul tuo sito web a meno che non riproducano il video."

#: includes/controls/groups/typography.php:190
msgctxt "Typography Control"
msgid "Underline"
msgstr "Sottolineato"

#: includes/widgets/heading.php:193
msgid "Add Your Heading Text Here"
msgstr "Aggiungi qui il testo del titolo"

#: includes/template-library/sources/local.php:496
#: includes/template-library/sources/local.php:612
#: includes/template-library/sources/local.php:762
#: includes/template-library/sources/local.php:771
msgid "Access denied."
msgstr "Accesso negato."

#: includes/settings/settings.php:288
msgid "Disable Default Fonts"
msgstr "Disabilita font predefiniti"

#: includes/controls/groups/flex-container.php:105
#: includes/controls/groups/flex-container.php:141
#: includes/controls/groups/flex-container.php:218
#: includes/controls/groups/flex-item.php:63
#: includes/controls/groups/flex-item.php:89
#: includes/controls/groups/grid-container.php:143
#: includes/controls/groups/grid-container.php:171
#: includes/controls/groups/grid-container.php:200
#: includes/controls/groups/grid-container.php:240
#: includes/elements/column.php:218 includes/widgets/accordion.php:427
#: includes/widgets/common-base.php:526 includes/widgets/counter.php:292
#: includes/widgets/counter.php:326 includes/widgets/counter.php:400
#: includes/widgets/counter.php:436 includes/widgets/icon-list.php:584
#: includes/widgets/image-carousel.php:755 includes/widgets/rating.php:211
#: includes/widgets/tabs.php:217 includes/widgets/tabs.php:247
#: includes/widgets/toggle.php:451 includes/widgets/traits/button-trait.php:151
#: includes/widgets/traits/button-trait.php:296
#: modules/floating-buttons/base/widget-floating-bars-base.php:586
#: modules/floating-buttons/base/widget-floating-bars-base.php:1197
#: modules/floating-buttons/base/widget-floating-bars-base.php:1296
#: modules/nested-accordion/widgets/nested-accordion.php:181
#: modules/nested-accordion/widgets/nested-accordion.php:221
#: modules/nested-tabs/widgets/nested-tabs.php:242
#: modules/nested-tabs/widgets/nested-tabs.php:284
#: modules/nested-tabs/widgets/nested-tabs.php:354
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:95
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:162
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:198
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:203
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:208
msgid "End"
msgstr "Fine"

#: includes/controls/groups/flex-container.php:97
#: includes/controls/groups/flex-container.php:133
#: includes/controls/groups/flex-container.php:210
#: includes/controls/groups/flex-item.php:55
#: includes/controls/groups/flex-item.php:85
#: includes/controls/groups/grid-container.php:135
#: includes/controls/groups/grid-container.php:163
#: includes/controls/groups/grid-container.php:192
#: includes/controls/groups/grid-container.php:232
#: includes/elements/column.php:216 includes/widgets/accordion.php:423
#: includes/widgets/common-base.php:518 includes/widgets/counter.php:288
#: includes/widgets/counter.php:318 includes/widgets/counter.php:392
#: includes/widgets/counter.php:428 includes/widgets/icon-list.php:576
#: includes/widgets/image-carousel.php:747 includes/widgets/rating.php:203
#: includes/widgets/tabs.php:209 includes/widgets/tabs.php:239
#: includes/widgets/toggle.php:447 includes/widgets/traits/button-trait.php:147
#: includes/widgets/traits/button-trait.php:288
#: modules/floating-buttons/base/widget-floating-bars-base.php:582
#: modules/floating-buttons/base/widget-floating-bars-base.php:1189
#: modules/floating-buttons/base/widget-floating-bars-base.php:1292
#: modules/nested-accordion/widgets/nested-accordion.php:173
#: modules/nested-accordion/widgets/nested-accordion.php:217
#: modules/nested-tabs/widgets/nested-tabs.php:234
#: modules/nested-tabs/widgets/nested-tabs.php:276
#: modules/nested-tabs/widgets/nested-tabs.php:346
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:93
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:160
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:196
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:201
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:206
msgid "Start"
msgstr "Inizio"

#: core/debug/classes/inspection-base.php:25
#: core/debug/loading-inspection-manager.php:44
msgid "The preview could not be loaded"
msgstr "L'anteprima non può essere caricata"

#: core/debug/loading-inspection-manager.php:43
msgid "We’re sorry, but something went wrong. Click on 'Learn more' and follow each of the steps to quickly solve it."
msgstr "Si è verificato un problema. Fai clic su \"Maggiori dettagli\" e segui le indicazioni per risolvere il problema rapidamente."

#: core/admin/admin-notices.php:147 core/admin/admin-notices.php:182
msgid "Update Notification"
msgstr "Aggiorna le notifiche"

#. Author URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"

#. Plugin URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"

#: modules/history/views/history-panel-template.php:17
msgid "Switch to Revisions tab for older versions"
msgstr "Passa alla scheda Revisioni per le versioni precedenti"

#: modules/history/views/history-panel-template.php:10
#: assets/js/editor.js:51843
msgid "Revisions"
msgstr "Revisioni"

#: includes/editor-templates/hotkeys.php:16
#: includes/editor-templates/templates.php:211
#: modules/history/views/history-panel-template.php:9 assets/js/editor.js:51840
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:1307
msgid "Actions"
msgstr "Azioni"

#: includes/editor-templates/hotkeys.php:137
#: includes/editor-templates/panel.php:85 assets/js/ai-admin.js:2054
#: assets/js/ai-gutenberg.js:2192 assets/js/ai-media-library.js:2054
#: assets/js/ai-unify-product-images.js:2054 assets/js/ai.js:2837
#: assets/js/editor.js:52435
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:17
msgid "History"
msgstr "Cronologia"

#: core/kits/documents/tabs/settings-lightbox.php:152
#: includes/widgets/video.php:917
msgid "UI Hover Color"
msgstr "Colore della UI al passaggio del mouse"

#: modules/history/views/history-panel-template.php:25
msgid "Once you start working, you'll be able to redo / undo any action you make in the editor."
msgstr "Mentre stai lavorando adesso puoi anche usare le funzioni ripeti / annulla delle azioni che fai nell'editor."

#: core/kits/documents/tabs/settings-lightbox.php:141
#: includes/widgets/video.php:905
msgid "UI Color"
msgstr "Colore UI"

#: modules/history/views/history-panel-template.php:24
msgid "No History Yet"
msgstr "Cronologia non disponibile"

#: includes/widgets/video.php:388
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:81
msgid "Mute"
msgstr "Silenzioso"

#: core/kits/documents/tabs/settings-lightbox.php:48
msgid "Open all image links in a lightbox popup window. The lightbox will automatically work on any link that leads to an image file."
msgstr "Apri tutti i collegamenti delle immagini in una finestra popup con lightbox. Il lightbox a questo punto si aprirà ogni qualvolta che un collegamento conduce ad una immagine."

#: core/kits/documents/tabs/settings-lightbox.php:45
msgid "Image Lightbox"
msgstr "Lightbox Immagine"

#: includes/template-library/sources/local.php:1018
msgid "Choose an Elementor template JSON file or a .zip archive of Elementor templates, and add them to the list of templates available in your library."
msgstr "Scegli un template in file JSON o un archivio .zip dei template di Elementor e aggiungili alla lista dei template disponibili nella tua libreria."

#: includes/settings/tools.php:420
msgid "Please Note: We do not recommend updating to a beta version on production sites."
msgstr "Nota: sconsigliamo di aggiornare ad una versione beta i siti di produzione."

#: includes/settings/tools.php:412
msgid "Beta Tester"
msgstr "Verificatore versioni beta"

#. translators: %s: Elementor version.
#: includes/settings/tools.php:372
msgid "Experiencing an issue with Elementor version %s? Rollback to a previous version before the issue appeared."
msgstr "Hai dei problemi con la versione %s di Elementor? Torna indietro alla versione precedente all'apparizione del problema."

#: includes/settings/tools.php:389
msgid "Warning: Please backup your database before making the rollback."
msgstr "Avviso: Fai un backup del database prima di effettuare il ripristino."

#: core/common/modules/finder/categories/settings.php:54
#: includes/settings/settings.php:304
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:5
msgid "Integrations"
msgstr "Integrazioni"

#: includes/controls/url.php:116
msgid "Add nofollow"
msgstr "Aggiungi nofollow"

#: includes/controls/url.php:103
msgid "Link Options"
msgstr "Opzioni link"

#: includes/controls/groups/box-shadow.php:72
msgctxt "Box Shadow Control"
msgid "Outline"
msgstr "Contorno"

#. Translators: %s: Element Name.
#. Translators: %s: Element name.
#. translators: %s: Element type title.
#: core/document-types/page-base.php:184 assets/js/atomic-widgets-editor.js:945
#: assets/js/editor.js:30438 assets/js/editor.js:30630
#: assets/js/editor.js:33069 assets/js/editor.js:33539
#: assets/js/editor.js:33640 assets/js/editor.js:33966
#: assets/js/editor.js:37493 assets/js/editor.js:48727
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:17
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:18
msgid "Edit %s"
msgstr "Modifica %s"

#: includes/settings/tools.php:396
msgid "Become a Beta Tester"
msgstr "Diventa un Beta Tester"

#: includes/settings/tools.php:381
msgid "Rollback Version"
msgstr "Versione di Ripristino"

#: core/common/modules/finder/categories/tools.php:77
#: includes/settings/tools.php:365
msgid "Version Control"
msgstr "Controllo Versione"

#: includes/rollback.php:165 includes/settings/tools.php:193
#: includes/settings/tools.php:368 assets/js/admin.js:2291
msgid "Rollback to Previous Version"
msgstr "Ripristina una Versione Precedente"

#: includes/elements/column.php:906 includes/elements/container.php:1850
#: includes/elements/section.php:1347 includes/widgets/common-base.php:867
#: modules/floating-buttons/base/widget-contact-button-base.php:1417
#: modules/floating-buttons/base/widget-floating-bars-base.php:916
msgid "Animation Delay"
msgstr "Ritardo Animazione"

#: includes/elements/column.php:797 includes/elements/container.php:1754
#: includes/elements/section.php:1258 includes/widgets/common-base.php:773
msgid "Z-Index"
msgstr "Z-Index"

#: core/kits/documents/tabs/theme-style-form-fields.php:137
#: core/kits/documents/tabs/theme-style-images.php:203
#: includes/base/element-base.php:1264
#: includes/controls/groups/background.php:673 includes/elements/column.php:361
#: includes/elements/column.php:521 includes/elements/column.php:629
#: includes/elements/container.php:733 includes/elements/container.php:947
#: includes/elements/container.php:1107 includes/elements/section.php:617
#: includes/elements/section.php:804 includes/elements/section.php:911
#: includes/widgets/alert.php:446 includes/widgets/common-base.php:933
#: includes/widgets/common-base.php:1048 includes/widgets/google-maps.php:252
#: includes/widgets/heading.php:400 includes/widgets/icon-box.php:482
#: includes/widgets/icon-box.php:717 includes/widgets/icon-list.php:470
#: includes/widgets/icon-list.php:697 includes/widgets/image-box.php:557
#: includes/widgets/image-box.php:685 includes/widgets/image.php:501
#: includes/widgets/text-editor.php:389
#: includes/widgets/traits/button-trait.php:451
#: modules/floating-buttons/base/widget-contact-button-base.php:1494
#: modules/floating-buttons/base/widget-contact-button-base.php:2233
#: modules/nested-tabs/widgets/nested-tabs.php:609
#: modules/shapes/widgets/text-path.php:460
#: modules/shapes/widgets/text-path.php:638
msgid "Transition Duration"
msgstr "Durata della transizione"

#: core/kits/documents/tabs/settings-layout.php:101
#: includes/elements/column.php:237
msgid "Widgets Space"
msgstr "Spaziatura widget"

#: includes/settings/tools.php:399
msgid "Turn-on Beta Tester, to get notified when a new beta version of Elementor or Elementor Pro is available. The Beta version will not install automatically. You always have the option to ignore it."
msgstr "Attiva il Beta Tester, per ricevere notifica quando viene rilasciata una nuova versione beta di Elementor o E-Pro. La versione Beta non si installa automaticamente. Hai sempre l'opzione per ignorarla."

#: includes/controls/url.php:112
msgid "Open in new window"
msgstr "Apri in una nuova finestra"

#: core/kits/documents/tabs/settings-layout.php:118
msgid "Sets the default space between widgets (Default: 20px)"
msgstr "Imposta la spaziatura predefinita tra i widget (Predefinito: 20px)"

#: includes/settings/settings.php:341
msgid "Switch Editor Loader Method"
msgstr "Cambia metodo di caricamento dell'Editor"

#: includes/settings/settings.php:419
msgid "Internal Embedding"
msgstr "Incorporato internamente"

#: includes/settings/settings.php:418
msgid "External File"
msgstr "File esterno"

#: includes/settings/settings.php:412
msgid "CSS Print Method"
msgstr "Metodo di stampa CSS"

#: includes/settings/settings.php:349
msgid "For troubleshooting server configuration conflicts."
msgstr "Per la risoluzione dei conflitti nella configurazione del server."

#: core/debug/inspector.php:55 includes/settings/settings.php:347
#: includes/settings/settings.php:359 includes/settings/settings.php:370
#: includes/settings/settings.php:434 includes/settings/settings.php:451
#: includes/settings/settings.php:463 includes/settings/tools.php:418
#: modules/generator-tag/module.php:81
#: modules/nested-tabs/widgets/nested-tabs.php:382
#: modules/safe-mode/module.php:48 assets/js/admin.js:294
#: assets/js/ai-admin.js:64 assets/js/ai-gutenberg.js:64
#: assets/js/ai-media-library.js:64 assets/js/ai-unify-product-images.js:64
#: assets/js/ai.js:64 assets/js/app-packages.js:1355 assets/js/app.js:1714
#: assets/js/common.js:64 assets/js/editor.js:40406
#: assets/js/packages/editor-controls/editor-controls.js:18
#: assets/js/packages/editor-controls/editor-controls.strings.js:109
msgid "Enable"
msgstr "Abilita"

#: core/debug/inspector.php:54 includes/settings/settings.php:346
#: includes/settings/settings.php:358 includes/settings/settings.php:371
#: includes/settings/settings.php:435 includes/settings/settings.php:452
#: includes/settings/settings.php:464 includes/settings/tools.php:417
#: modules/element-cache/module.php:146 modules/generator-tag/module.php:82
#: modules/nested-tabs/widgets/nested-tabs.php:381
#: modules/safe-mode/module.php:47
msgid "Disable"
msgstr "Disabilita"

#: core/base/document.php:2002 modules/ai/preferences.php:67
#: assets/js/element-manager-admin.js:655
#: assets/js/element-manager-admin.js:732
msgid "Status"
msgstr "Stato"

#: includes/widgets/common-base.php:1308 includes/widgets/spacer.php:130
#: includes/widgets/text-editor.php:503
msgid "Space"
msgstr "Spazio"

#: core/kits/documents/tabs/settings-layout.php:139
msgid "Page Title Selector"
msgstr "Selettore del titolo della pagina"

#: includes/admin-templates/new-template.php:75
#: includes/settings/controls.php:155
msgid "Select"
msgstr "Seleziona"

#: includes/widgets/text-editor.php:148 includes/widgets/text-editor.php:410
msgid "Drop Cap"
msgstr "Capolettera"

#: core/common/modules/finder/categories/edit.php:118 assets/js/editor.js:10620
#: assets/js/editor.js:19318
msgid "Template"
msgstr "Template"

#: core/document-types/page-base.php:96
msgid "Hide Title"
msgstr "Nascondi il titolo"

#: core/settings/editor-preferences/model.php:107
msgid "Canvas"
msgstr "Canvas"

#: includes/maintenance-mode.php:283
msgid "Maintenance Mode ON"
msgstr "Modalità di manutenzione Attiva"

#: includes/maintenance-mode.php:251
msgid "Choose Template"
msgstr "Seleziona un template"

#: includes/maintenance-mode.php:370
msgid "To enable maintenance mode you have to set a template for the maintenance mode page."
msgstr "Per abilitare la modalità di manutenzione devi impostare un template per la pagina della modalità di manutenzione."

#: includes/maintenance-mode.php:292 includes/maintenance-mode.php:369
#: includes/template-library/sources/local.php:231 assets/js/app.js:10576
msgid "Edit Template"
msgstr "Modifica il template"

#: includes/maintenance-mode.php:243
msgid "Roles"
msgstr "Ruoli"

#: includes/maintenance-mode.php:237
msgid "Logged In"
msgstr "Connesso"

#: includes/maintenance-mode.php:231
msgid "Who Can Access"
msgstr "Chi può accedere"

#: includes/maintenance-mode.php:223
msgid "Maintenance Mode returns HTTP 503 code, so search engines know to come back a short time later. It is not recommended to use this mode for more than a couple of days."
msgstr "Se la modalità di manutenzione restituisce il codice HTTP 503, i motori di ricerca capiscono che devono ritornare dopo poco tempo. Non è consigliabile di utilizzare questa modalità per più di un paio di giorni."

#: includes/maintenance-mode.php:217
msgid "Maintenance"
msgstr "Manutenzione"

#: core/kits/documents/kit.php:154 includes/maintenance-mode.php:215
#: assets/js/editor.js:52112
msgid "Disabled"
msgstr "Disabilitato"

#: includes/maintenance-mode.php:210
msgid "Choose Mode"
msgstr "Seleziona la modalità"

#: core/common/modules/finder/categories/tools.php:62
#: includes/maintenance-mode.php:201 includes/maintenance-mode.php:205
msgid "Maintenance Mode"
msgstr "Modalità di manutenzione"

#: includes/maintenance-mode.php:226
msgid "Coming Soon returns HTTP 200 code, meaning the site is ready to be indexed."
msgstr "Se Prossima apertura restituisce il codice HTTP 200, significa che il sito è pronto per essere indicizzato."

#: includes/maintenance-mode.php:216
msgid "Coming Soon"
msgstr "Prossima apertura"

#: includes/maintenance-mode.php:206
msgid "Set your entire website as MAINTENANCE MODE, meaning the site is offline temporarily for maintenance, or set it as COMING SOON mode, meaning the site is offline until it is ready to be launched."
msgstr "Impostare il tuo intero sito nella MODALITÀ DI MANUTENZIONE, significa che il sito sarà temporaneamente fuori servizio per manutenzione, mentre impostando la modalità Prossima apertura il sito sarà fuori servizio finchè non sarà pronto per essere pubblicato."

#: includes/editor-templates/hotkeys.php:54
msgid "Paste Style"
msgstr "Incolla Stile"

#: core/kits/documents/tabs/settings-layout.php:143
msgid "Elementor lets you hide the page title. This works for themes that have \"h1.entry-title\" selector. If your theme's selector is different, please enter it above."
msgstr "Elementor ti permette di nascondere il titolo della pagina. Funziona con i temi che hanno il selettore \"h1.entry-title\". Se il selettore del tuo tema è diverso, inseriscilo qui sopra."

#: includes/maintenance-mode.php:220
msgid "Choose between Coming Soon mode (returning HTTP 200 code) or Maintenance Mode (returning HTTP 503 code)."
msgstr "Scegli tra la modalità Coming Soon - In arrivo presto - (ritorno del codice HTTP 200) o la modalità di manutenzione (ritorno del codice HTTP 503)."

#: includes/elements/container.php:1326 includes/elements/section.php:1109
msgid "Bring to Front"
msgstr "Sposta in avanti"

#: core/kits/documents/tabs/settings-lightbox.php:18
#: includes/widgets/image-carousel.php:387
#: includes/widgets/image-gallery.php:200 includes/widgets/image.php:225
#: includes/widgets/video.php:733 includes/widgets/video.php:881
msgid "Lightbox"
msgstr "Lightbox"

#: includes/controls/groups/flex-container.php:109
#: includes/controls/groups/flex-container.php:222
#: includes/controls/groups/grid-container.php:204
#: includes/controls/groups/grid-container.php:244
#: includes/elements/column.php:191 includes/elements/column.php:219
#: includes/elements/section.php:428 includes/widgets/icon-list.php:238
#: includes/widgets/toggle.php:317
msgid "Space Between"
msgstr "Spazio intermedio"

#: includes/shapes.php:219
msgctxt "Shapes"
msgid "Book"
msgstr "A libro"

#: includes/widgets/icon-list.php:230
msgid "List"
msgstr "Elenco"

#: includes/shapes.php:229
msgctxt "Shapes"
msgid "Arrow"
msgstr "Freccia"

#: includes/shapes.php:203
msgctxt "Shapes"
msgid "Waves"
msgstr "Onde"

#: includes/shapes.php:197
msgctxt "Shapes"
msgid "Curve Asymmetrical"
msgstr "Curva asimmetrica"

#: includes/shapes.php:192
msgctxt "Shapes"
msgid "Curve"
msgstr "Curva"

#: includes/shapes.php:171
msgctxt "Shapes"
msgid "Triangle Asymmetrical"
msgstr "Triangolo asimmetrico"

#: includes/shapes.php:166
msgctxt "Shapes"
msgid "Triangle"
msgstr "Triangolo"

#: includes/shapes.php:160
msgctxt "Shapes"
msgid "Pyramids"
msgstr "Piramidi"

#: includes/shapes.php:156 includes/widgets/divider.php:189
#: includes/widgets/divider.php:319
msgctxt "Shapes"
msgid "Zigzag"
msgstr "Zigzag"

#: includes/shapes.php:149
msgctxt "Shapes"
msgid "Clouds"
msgstr "Nuvole"

#: includes/shapes.php:142
msgctxt "Shapes"
msgid "Drops"
msgstr "Gocce"

#: includes/shapes.php:137
msgctxt "Shapes"
msgid "Mountains"
msgstr "Montagne"

#: includes/elements/container.php:1313 includes/elements/section.php:1096
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:61
msgid "Invert"
msgstr "Inverti"

#: includes/elements/container.php:1299 includes/elements/section.php:1082
msgid "Flip"
msgstr "Flip"

#: includes/shapes.php:224
msgctxt "Shapes"
msgid "Split"
msgstr "A metà"

#: includes/shapes.php:209
msgctxt "Shapes"
msgid "Waves Brush"
msgstr "Pennellate"

#: includes/shapes.php:177
msgctxt "Shapes"
msgid "Tilt"
msgstr "Diagonale"

#: includes/shapes.php:183
msgctxt "Shapes"
msgid "Tilt Opacity"
msgstr "Diagonali sfumate"

#: includes/shapes.php:188
msgctxt "Shapes"
msgid "Fan Opacity"
msgstr "Ventaglio sfumato"

#: includes/shapes.php:214
msgctxt "Shapes"
msgid "Waves Pattern"
msgstr "Onde variabili"

#: includes/elements/container.php:1173 includes/elements/section.php:956
msgid "Shape Divider"
msgstr "Separatore sagomato"

#: includes/widgets/tabs.php:275
msgid "Navigation Width"
msgstr "Larghezza della barra di navigazione"

#: includes/elements/column.php:818 includes/elements/container.php:1775
#: includes/elements/section.php:1279 includes/widgets/common-base.php:793
#: includes/widgets/traits/button-trait.php:214
#: modules/floating-buttons/base/widget-contact-button-base.php:3099
#: modules/floating-buttons/base/widget-floating-bars-base.php:1505
#: modules/nested-accordion/widgets/nested-accordion.php:138
#: modules/nested-tabs/widgets/nested-tabs.php:160
msgid "Add your custom id WITHOUT the Pound key. e.g: my-id"
msgstr "Aggiungi il tuo id personalizzato senza il simbolo cancelletto. Es.: il-mio-id"

#: includes/elements/column.php:809 includes/elements/container.php:1766
#: includes/elements/section.php:1270 includes/widgets/common-base.php:784
#: modules/floating-buttons/base/widget-contact-button-base.php:3090
#: modules/floating-buttons/base/widget-floating-bars-base.php:1496
#: modules/nested-accordion/widgets/nested-accordion.php:129
#: modules/nested-tabs/widgets/nested-tabs.php:151
msgid "CSS ID"
msgstr "CSS ID"

#: core/kits/documents/tabs/theme-style-buttons.php:166
#: core/kits/documents/tabs/theme-style-images.php:134
#: core/kits/documents/tabs/theme-style-typography.php:148
#: includes/base/element-base.php:883 includes/elements/column.php:346
#: includes/elements/column.php:475 includes/elements/column.php:594
#: includes/elements/container.php:718 includes/elements/container.php:899
#: includes/elements/container.php:1060 includes/elements/section.php:602
#: includes/elements/section.php:758 includes/elements/section.php:876
#: includes/widgets/alert.php:429 includes/widgets/common-base.php:918
#: includes/widgets/common-base.php:1013 includes/widgets/google-maps.php:237
#: includes/widgets/heading.php:382 includes/widgets/icon-box.php:440
#: includes/widgets/icon-box.php:694 includes/widgets/icon-list.php:450
#: includes/widgets/icon-list.php:678 includes/widgets/icon.php:256
#: includes/widgets/image-box.php:524 includes/widgets/image-box.php:662
#: includes/widgets/image.php:468 includes/widgets/text-editor.php:371
#: includes/widgets/traits/button-trait.php:393
#: modules/floating-buttons/base/widget-contact-button-base.php:1253
#: modules/floating-buttons/base/widget-contact-button-base.php:2012
#: modules/floating-buttons/base/widget-contact-button-base.php:2494
#: modules/floating-buttons/base/widget-contact-button-base.php:2674
#: modules/floating-buttons/base/widget-floating-bars-base.php:690
#: modules/floating-buttons/base/widget-floating-bars-base.php:1394
#: modules/nested-accordion/widgets/nested-accordion.php:663
#: modules/nested-accordion/widgets/nested-accordion.php:726
#: modules/nested-tabs/widgets/nested-tabs.php:545
#: modules/nested-tabs/widgets/nested-tabs.php:773
#: modules/nested-tabs/widgets/nested-tabs.php:954
#: modules/shapes/widgets/text-path.php:433
#: modules/shapes/widgets/text-path.php:572
msgid "Hover"
msgstr "Hover"

#: includes/controls/groups/background.php:184
#: includes/controls/groups/background.php:213
msgctxt "Background Control"
msgid "Location"
msgstr "Posizione"

#: includes/controls/groups/background.php:230
msgctxt "Background Control"
msgid "Type"
msgstr "Tipo"

#: core/admin/admin.php:378
msgid "Video Tutorials"
msgstr "Video Tutorial"

#: core/admin/admin.php:378
msgid "View Elementor Video Tutorials"
msgstr "Visualizza i video tutorial di Elementor"

#: core/admin/admin.php:377
msgid "View Elementor Documentation"
msgstr "Visualizza la documentazione di Elementor"

#: core/admin/admin.php:377
msgid "Docs & FAQs"
msgstr "Documentazione e FAQ"

#: includes/settings/settings.php:292
msgid "Checking this box will disable Elementor's Default Fonts, and make Elementor inherit the fonts from your theme."
msgstr "Selezionando questa casella disabiliterai i font predefiniti di Elementor, ed Elementor utilizzerà i font del tuo tema."

#: includes/settings/settings.php:284
msgid "Checking this box will disable Elementor's Default Colors, and make Elementor inherit the colors from your theme."
msgstr "Selezionando questa casella disabiliterai i colori predefiniti di Elementor, ed Elementor utilizzerà i colori del tuo tema."

#. translators: 1: Human readable time difference, 2: Date.
#: modules/history/revisions-manager.php:179
msgid "%1$s ago (%2$s)"
msgstr "%1$s fa (%2$s)"

#: modules/history/revisions-manager.php:160
msgid "Autosave"
msgstr "Salvataggio automatico"

#: modules/history/revisions-manager.php:163
msgid "Revision"
msgstr "Revisione"

#: modules/apps/admin-apps-page.php:177
#: modules/history/views/revisions-panel-template.php:55
msgid "By"
msgstr "Da"

#: includes/settings/tools.php:356
msgid "Enter your old and new URLs for your WordPress installation, to update all Elementor data (Relevant for domain transfers or move to 'HTTPS')."
msgstr "Inserisci il vecchio e il nuovo URL della tua installazione di WordPress, per aggiornare tutti i dati di Elementor (importate per i trasferimenti di dominio o per spostarlo su 'HTTPS')."

#: includes/settings/tools.php:352
msgid "Update Site Address (URL)"
msgstr "Aggiorna l'indirizzo del sito (URL)"

#: core/common/modules/finder/categories/tools.php:56
#: includes/settings/tools.php:334 includes/settings/tools.php:338
#: includes/settings/tools.php:355
msgid "Replace URL"
msgstr "Sostituzione dell'URL"

#: modules/history/revisions-manager.php:151
msgctxt "revision date format"
msgid "M j @ H:i"
msgstr "M j @ H:i"

#: modules/history/views/revisions-panel-template.php:26
msgid "Revision history lets you save your previous versions of your work, and restore them any time."
msgstr "La cronologia delle revisioni consente di salvare le versioni precedenti del tuo lavoro e ripristinarli in qualsiasi momento."

#: modules/history/views/revisions-panel-template.php:39
msgid "No Revisions Saved Yet"
msgstr "Non è stata ancora salvata alcuna revisione"

#: modules/history/views/revisions-panel-template.php:27
msgid "Start designing your page and you will be able to see the entire revision history here."
msgstr "Inizia a progettare la tua pagina e potrai vedere l'intera cronologia delle revisioni qui."

#: modules/history/views/revisions-panel-template.php:28
msgid "It looks like the post revision feature is unavailable in your website."
msgstr "Sembra che la funzione revisione dell'articolo non sia disponibile sul tuo sito web."

#. translators: 1: Minimum recommended_memory, 2: Preferred memory, 3:
#. WordPress wp-config memory documentation.
#: modules/system-info/reporters/server.php:170
msgid "We recommend setting memory to at least %1$s. (%2$s or higher is preferred) For more information, read about <a href=\"%3$s\">how to increase memory allocated to PHP</a>."
msgstr "Si consiglia di impostare la memoria su almeno %1$s. (%2$s o superiore è preferibile) Per ulteriori informazioni, leggere <a href=\"%3$s\">come aumentare la memoria allocata a PHP</a>."

#: includes/widgets/counter.php:199
msgid "Thousand Separator"
msgstr "Separatore di migliaia"

#: includes/managers/controls.php:1093
msgid "Meet Our Custom CSS"
msgstr "Guarda il nostro CSS personalizzato"

#: core/kits/documents/tabs/settings-custom-css.php:17
#: includes/managers/controls.php:1075
msgid "Custom CSS"
msgstr "CSS personalizzato"

#: includes/editor-templates/panel-elements.php:102
msgid "With this feature, you can save a widget as global, then add it to multiple areas. All areas will be editable from one single place."
msgstr "Con questa funzionalità puoi salvare un widget come globale e poi aggiungerlo a molteplici aree. Tutte le aree saranno modificabili da un singolo posto."

#: includes/editor-templates/panel-elements.php:101
msgid "Meet Our Global Widget"
msgstr "Guarda il nostro widget globale"

#: modules/promotions/widgets/pro-widget-promotion.php:76
#: assets/js/ai-admin.js:7911 assets/js/ai-gutenberg.js:8129
#: assets/js/ai-layout.js:3402 assets/js/ai-media-library.js:7911
#: assets/js/ai-unify-product-images.js:7911 assets/js/ai.js:9372
msgid "Go Pro"
msgstr "Passa a Pro"

#: includes/editor-templates/panel-elements.php:28
msgid "Get more with Elementor Pro"
msgstr "Ottieni di più con Elementor Pro"

#: includes/base/element-base.php:927 includes/base/element-base.php:1091
#: includes/widgets/common-base.php:1092 includes/widgets/icon-list.php:284
#: includes/widgets/icon.php:329 includes/widgets/text-editor.php:150
#: includes/widgets/video.php:736 modules/shapes/widgets/text-path.php:220
msgid "Off"
msgstr "Off"

#: includes/base/element-base.php:926 includes/base/element-base.php:1090
#: includes/widgets/common-base.php:1091 includes/widgets/icon-list.php:285
#: includes/widgets/icon.php:330 includes/widgets/text-editor.php:151
#: includes/widgets/video.php:737 modules/shapes/widgets/text-path.php:219
msgid "On"
msgstr "On"

#: includes/managers/controls.php:1081
msgid "Custom CSS lets you add CSS code to any widget, and see it render live right in the editor."
msgstr "Il CSS personalizzato ti permette di aggiungere del codice CSS a ciascun widget e vedere le modifiche direttamente nell'editor."

#: includes/settings/settings.php:298
msgid "Improve Elementor"
msgstr "Migliora Elementor"

#: includes/widgets/traits/button-trait.php:34
msgid "Extra Small"
msgstr "Molto Piccolo"

#: includes/widgets/traits/button-trait.php:38
msgid "Extra Large"
msgstr "Molto Grande"

#: includes/frontend.php:1228
msgid "Invalid Data: The Template ID cannot be the same as the currently edited template. Please choose a different one."
msgstr "Dati non validi: L'ID del template non può essere uguale al template che stai modificando. Devi sceglierne una diversa."

#: includes/base/widget-base.php:312 includes/base/widget-base.php:321
msgid "Skin"
msgstr "Aspetto"

#: includes/editor-templates/panel.php:162
msgid "Update changes to page"
msgstr "Aggiorna modifiche alla pagina"

#: includes/editor-templates/panel.php:198
msgid "%s are disabled"
msgstr "%s sono disabilitati"

#: core/admin/admin-notices.php:243
msgid "No thanks"
msgstr "No grazie"

#: core/kits/documents/tabs/settings-layout.php:161
msgid "Enter parent element selector to which stretched sections will fit to (e.g. #primary / .wrapper / main etc). Leave blank to fit to page width."
msgstr "Immetti il selettore dell'elemento genitore al quale le sezioni allungate devono adattarsi (es. #primary / .wrapper / main ecc). Lasciare vuoto per adattarsi alla larghezza della pagina."

#: includes/elements/section.php:464
msgid "Stretch Section"
msgstr "Estendi la sezione"

#: core/kits/documents/tabs/settings-layout.php:158
msgid "Stretched Section Fit To"
msgstr "Adattare sezione allargata a"

#: includes/elements/section.php:472
msgid "Stretch the section to the full width of the page using JS."
msgstr "Estendi la sezione all'intera larghezza della pagina attraverso JS."

#: core/kits/documents/tabs/settings-layout.php:74
msgid "Sets the default width of the content area (Default: 1140px)"
msgstr "Imposta la larghezza predefinita dell'area del contenuto (Predefinito: 1140px)"

#: core/admin/admin-notices.php:231
msgid "Learn more."
msgstr "Approfondisci."

#: includes/elements/section.php:1378
msgid "Reverse Columns"
msgstr "Inverti colonne"

#: includes/controls/dimensions.php:141 includes/controls/dimensions.php:144
msgid "Link values together"
msgstr "Collega insieme i valori"

#: includes/widgets/shortcode.php:42 includes/widgets/shortcode.php:103
msgid "Shortcode"
msgstr "Shortcode"

#: core/base/document.php:172 includes/editor-templates/global.php:21
#: includes/editor-templates/responsive-bar.php:65 includes/frontend.php:1388
#: assets/js/152f977e0c1304a3b0db.bundle.js:208 assets/js/ai-admin.js:586
#: assets/js/ai-gutenberg.js:724 assets/js/ai-layout.js:418
#: assets/js/ai-media-library.js:586 assets/js/ai-unify-product-images.js:586
#: assets/js/ai.js:1369 assets/js/app-packages.js:525
#: assets/js/app-packages.js:2564 assets/js/app-packages.js:3122
#: assets/js/app.js:593 assets/js/app.js:2835 assets/js/app.js:3257
#: assets/js/app.js:6161 assets/js/app.js:6194 assets/js/app.js:12275
#: assets/js/editor.js:47931 assets/js/import-export-admin.js:313
msgid "Close"
msgstr "Chiudi"

#: core/document-types/page.php:58 modules/library/documents/page.php:57
#: assets/js/editor.js:8482
#: assets/js/packages/editor-global-classes/editor-global-classes.js:6
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:58
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:2
msgid "Page"
msgstr "Pagina"

#: core/common/modules/connect/apps/library.php:16 assets/js/editor.js:9621
#: assets/js/import-export-admin.js:300
msgid "Library"
msgstr "Libreria"

#: includes/editor-templates/templates.php:305
#: includes/editor-templates/templates.php:393
#: includes/editor-templates/templates.php:439
#: includes/editor-templates/templates.php:453 assets/js/ai-admin.js:6705
#: assets/js/ai-gutenberg.js:6923 assets/js/ai-media-library.js:6705
#: assets/js/ai-unify-product-images.js:6705 assets/js/ai.js:8166
#: assets/js/editor.js:6426
msgid "Insert"
msgstr "Inserisci"

#: app/modules/import-export-customization/module.php:140
#: app/modules/import-export/module.php:142
#: includes/editor-templates/templates.php:328
#: includes/editor-templates/templates.php:361
#: includes/editor-templates/templates.php:418
#: includes/template-library/sources/local.php:1203 assets/js/app.js:5536
#: assets/js/app.js:5584 assets/js/app.js:5656 assets/js/app.js:6120
#: assets/js/app.js:12435
msgid "Export"
msgstr "Esporta"

#: core/common/modules/finder/categories/tools.php:28
#: core/common/modules/finder/categories/tools.php:50
#: includes/settings/admin-menu-items/tools-menu-item.php:29
#: includes/settings/admin-menu-items/tools-menu-item.php:33
#: includes/settings/tools.php:32 includes/settings/tools.php:33
#: includes/settings/tools.php:453
msgid "Tools"
msgstr "Strumenti"

#: includes/template-library/sources/local.php:209
msgid "Local"
msgstr "Locale"

#: includes/template-library/sources/local.php:280
msgctxt "Template Library"
msgid "Type"
msgstr "Tipi"

#: includes/editor-templates/templates.php:19 includes/settings/tools.php:322
#: includes/settings/tools.php:325
msgid "Sync Library"
msgstr "Sincronizza Libreria"

#: includes/template-library/sources/local.php:228
msgctxt "Template Library"
msgid "Template"
msgstr "Template"

#: includes/settings/tools.php:326
msgid "Elementor Library automatically updates on a daily basis. You can also manually update it by clicking on the sync button."
msgstr "La Libreria di Elementor si aggiorna ogni giorno automaticamente. Puoi anche aggiornarla manualmente facendo clic sul pulsante Sincronizza Libreria."

#: includes/editor-templates/templates.php:221
msgid "Stay tuned! More awesome templates coming real soon."
msgstr "Resta in contatto! Molti altri splendidi template sono in arrivo prestissimo."

#: includes/editor-templates/templates.php:484
msgid "Enter Template Name"
msgstr "Immetti il nome del template"

#: includes/editor-templates/global.php:45
msgid "Add Template"
msgstr "Aggiungi template"

#: includes/template-library/sources/local.php:1016
msgid "Import Templates"
msgstr "Importa template"

#: includes/template-library/sources/local.php:988
msgid "Export Template"
msgstr "Esporta template"

#: includes/template-library/sources/remote.php:61
msgid "Remote"
msgstr "Remoto"

#: includes/template-library/sources/cloud.php:106
#: includes/template-library/sources/cloud.php:327
#: includes/template-library/sources/local.php:500
msgid "(no title)"
msgstr "(senza titolo)"

#: includes/editor-templates/templates.php:41
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:1633
msgid "Back to Library"
msgstr "Torna alla libreria"

#: includes/editor-templates/hotkeys.php:174
#: includes/editor-templates/templates.php:554
#: includes/editor-templates/templates.php:570
#: includes/editor-templates/templates.php:584
msgid "Template Library"
msgstr "Libreria Template"

#: core/common/modules/finder/categories/general.php:49
#: includes/template-library/sources/admin-menu-items/saved-templates-menu-item.php:23
#: includes/template-library/sources/local.php:1731 assets/js/app.js:6336
#: assets/js/app.js:11342
msgid "Saved Templates"
msgstr "Template salvati"

#: includes/template-library/sources/local.php:1025
msgid "Import Now"
msgstr "Importa adesso"

#. translators: %s: WordPress child themes documentation.
#: modules/system-info/reporters/theme.php:207
msgid "If you want to modify the source code of your theme, we recommend using a <a href=\"%s\">child theme</a>."
msgstr "Se vuoi modificare il codice sorgente del tuo tema ti consigliamo di utilizzare un <a href=\"%s\">tema child</a>."

#: core/admin/admin-notices.php:371 modules/apps/admin-apps-page.php:187
#: modules/safe-mode/module.php:359 modules/safe-mode/module.php:368
#: modules/safe-mode/mu-plugin/elementor-safe-mode.php:105
#: assets/js/app-packages.js:1229 assets/js/app-packages.js:4375
#: assets/js/app-packages.js:4483 assets/js/app.js:1588 assets/js/app.js:4693
#: assets/js/app.js:6192 assets/js/app.js:7824 assets/js/app.js:8145
#: assets/js/app.js:9194 assets/js/app.js:10731 assets/js/app.js:11061
#: assets/js/app.js:11107 assets/js/app.js:12274 assets/js/editor.js:15016
#: assets/js/editor.js:28536 assets/js/editor.js:28567
#: assets/js/editor.js:41143 assets/js/element-manager-admin.js:542
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4018
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4773
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:27
msgid "Learn More"
msgstr "Approfondisci"

#: core/kits/documents/tabs/global-typography.php:28
#: core/kits/documents/tabs/global-typography.php:47 assets/js/app.js:6344
#: assets/js/app.js:11360 assets/js/editor.js:47682
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:222
msgid "Global Fonts"
msgstr "Font globali"

#: modules/floating-buttons/base/widget-contact-button-base.php:2216
#: assets/js/ai-admin.js:3596 assets/js/ai-gutenberg.js:3734
#: assets/js/ai-media-library.js:3596 assets/js/ai-unify-product-images.js:3596
#: assets/js/ai.js:4412
msgid "Animation"
msgstr "Animazione"

#: core/base/traits/shared-widget-controls-trait.php:289
#: includes/widgets/icon-box.php:497 includes/widgets/icon.php:294
#: includes/widgets/image-box.php:578 includes/widgets/image.php:519
#: includes/widgets/social-icons.php:577
#: includes/widgets/traits/button-trait.php:466
#: modules/floating-buttons/base/widget-contact-button-base.php:1476
#: modules/floating-buttons/base/widget-contact-button-base.php:2512
#: modules/floating-buttons/base/widget-floating-bars-base.php:748
#: modules/nested-tabs/widgets/nested-tabs.php:601
#: modules/shapes/widgets/text-path.php:452
msgid "Hover Animation"
msgstr "Animazione al passaggio del mouse"

#: includes/elements/column.php:892 includes/elements/container.php:1836
#: includes/elements/section.php:1333 includes/widgets/common-base.php:853
#: modules/floating-buttons/base/widget-contact-button-base.php:1406
#: modules/floating-buttons/base/widget-contact-button-base.php:2789
#: modules/floating-buttons/base/widget-floating-bars-base.php:887
msgid "Slow"
msgstr "Lento"

#: includes/elements/column.php:894 includes/elements/container.php:1838
#: includes/elements/section.php:1335 includes/widgets/common-base.php:855
#: modules/floating-buttons/base/widget-contact-button-base.php:1408
#: modules/floating-buttons/base/widget-contact-button-base.php:2791
#: modules/floating-buttons/base/widget-floating-bars-base.php:889
msgid "Fast"
msgstr "Veloce"

#: includes/controls/box-shadow.php:73 includes/controls/text-shadow.php:76
#: includes/widgets/tabs.php:189
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:77
msgid "Vertical"
msgstr "Verticale"

#: includes/controls/box-shadow.php:78 includes/controls/text-shadow.php:66
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:49
#: assets/js/packages/editor-controls/editor-controls.strings.js:78
#: assets/js/packages/editor-controls/editor-controls.strings.js:123
msgid "Blur"
msgstr "Sfoca"

#: includes/controls/box-shadow.php:68 includes/controls/text-shadow.php:71
#: includes/widgets/tabs.php:193
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:76
msgid "Horizontal"
msgstr "Orizzontale"

#: includes/controls/box-shadow.php:83
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:79
msgid "Spread"
msgstr "Diffondi"

#: includes/elements/column.php:879 includes/elements/container.php:1823
#: includes/elements/section.php:1320 includes/widgets/common-base.php:840
#: includes/widgets/video.php:929
#: modules/floating-buttons/base/widget-contact-button-base.php:1392
#: modules/floating-buttons/base/widget-floating-bars-base.php:873
msgid "Entrance Animation"
msgstr "Ingresso animazione"

#: includes/controls/groups/box-shadow.php:73
msgctxt "Box Shadow Control"
msgid "Inset"
msgstr "Inset"

#: includes/settings/settings.php:280
msgid "Disable Default Colors"
msgstr "Disattiva i colori predefiniti"

#: includes/widgets/icon-box.php:152 includes/widgets/icon.php:150
#: includes/widgets/social-icons.php:285
#: modules/floating-buttons/base/widget-contact-button-base.php:2086
#: modules/floating-buttons/base/widget-contact-button-base.php:2177
#: modules/floating-buttons/base/widget-contact-button-base.php:2870
#: modules/floating-buttons/base/widget-floating-bars-base.php:845
#: modules/link-in-bio/base/widget-link-in-bio-base.php:119
msgid "Rounded"
msgstr "Arrotondato"

#: includes/widgets/testimonial.php:46 includes/widgets/testimonial.php:132
msgid "Testimonial"
msgstr "Testimonianza"

#: includes/widgets/progress.php:123
msgid "My Skill"
msgstr "Le mie abilità"

#: includes/widgets/social-icons.php:212 includes/widgets/social-icons.php:365
msgid "Official Color"
msgstr "Colori ufficiali"

#: includes/widgets/social-icons.php:42 includes/widgets/social-icons.php:107
#: includes/widgets/social-icons.php:250
msgid "Social Icons"
msgstr "Icone social"

#: includes/widgets/testimonial.php:222
msgid "Aside"
msgstr "A lato"

#: core/kits/documents/tabs/global-colors.php:123
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:185
msgid "Custom Colors"
msgstr "Colori personalizzati"

#: core/base/providers/social-network-provider.php:168
#: includes/widgets/audio.php:53 includes/widgets/audio.php:104
msgid "SoundCloud"
msgstr "SoundCloud"

#: core/admin/feedback.php:93
msgid "I found a better plugin"
msgstr "Ho trovato un plugin migliore"

#: core/admin/feedback.php:97
msgid "I couldn't get the plugin to work"
msgstr "Non sono riuscito a far funzionare il plugin"

#: core/admin/feedback.php:101
msgid "It's a temporary deactivation"
msgstr "Si tratta di una disattivazione temporanea"

#: core/admin/feedback.php:110
msgid "Other"
msgstr "Altro"

#: includes/elements/section.php:290
msgid "Extended"
msgstr "Esteso"

#: includes/widgets/audio.php:218
msgid "Comments"
msgstr "Commenti"

#: core/admin/feedback.php:127
msgid "If you have a moment, please share why you are deactivating Elementor:"
msgstr "Se hai un attimo di tempo condividi perchè stai disattivando Elementor:"

#: core/admin/feedback.php:111
msgid "Please share the reason"
msgstr "Condividi la tua motivazione"

#. translators: %s: Elementor version.
#: core/admin/admin-notices.php:138
msgid "View Elementor version %s details"
msgstr "Visualizza i dettagli della versione %s di Elementor"

#: core/admin/feedback.php:94
msgid "Please share which plugin"
msgstr "Condividi quale plugin"

#: includes/widgets/audio.php:240
#: modules/floating-buttons/base/widget-contact-button-base.php:358
#: modules/floating-buttons/base/widget-contact-button-base.php:911
#: modules/link-in-bio/base/widget-link-in-bio-base.php:523
#: modules/link-in-bio/base/widget-link-in-bio-base.php:777
msgid "Username"
msgstr "Nome utente"

#: includes/widgets/audio.php:130
msgid "Visual Player"
msgstr "Lettore visuale"

#: includes/elements/column.php:388 includes/elements/container.php:772
#: includes/elements/section.php:644
#: modules/floating-buttons/base/widget-floating-bars-base.php:981
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1483
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:142
msgid "Background Overlay"
msgstr "Sfondo overlay"

#: includes/widgets/audio.php:207
msgid "Share Button"
msgstr "Pulsante Condividi"

#: includes/widgets/audio.php:182 includes/widgets/video.php:583
msgid "Download Button"
msgstr "Pulsante Scarica"

#: includes/widgets/audio.php:171
msgid "Like Button"
msgstr "Pulsante Mi piace"

#: includes/widgets/audio.php:160
msgid "Buy Button"
msgstr "Pulsante Acquista"

#: core/admin/admin-notices.php:143 core/admin/admin-notices.php:151
#: core/base/db-upgrades-manager.php:97
msgid "Update Now"
msgstr "Aggiorna ora"

#. translators: 1: Details URL, 2: Accessibility text, 3: Version number, 4:
#. Update URL, 5: Accessibility text.
#: core/admin/admin-notices.php:134
msgid "There is a new version of Elementor Page Builder available. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">View version %3$s details</a> or <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">update now</a>."
msgstr "È disponibile una nuova versione di Elementor Page Builder. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">Vedi i dettagli della versione %3$s</a> oppure <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">aggiorna ora</a>."

#: includes/widgets/audio.php:229
msgid "Play Counts"
msgstr "Contatore riproduzioni"

#: core/admin/feedback.php:119
msgid "Quick Feedback"
msgstr "Feedback veloce"

#: core/admin/feedback.php:89
msgid "I no longer need the plugin"
msgstr "Non ho più bisogno del plugin"

#: includes/elements/section.php:292
msgid "Wider"
msgstr "Più largo"

#: core/common/modules/finder/categories/general.php:29
#: core/role-manager/role-manager.php:69 includes/managers/elements.php:302
#: includes/settings/settings.php:252 includes/settings/settings.php:255
#: includes/settings/tools.php:309
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:17
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:19
msgid "General"
msgstr "Generale"

#: core/kits/documents/tabs/settings-background.php:18
#: includes/elements/column.php:276 includes/elements/container.php:643
#: includes/elements/section.php:533 includes/widgets/accordion.php:326
#: includes/widgets/accordion.php:496 includes/widgets/common-base.php:891
#: includes/widgets/toggle.php:358 includes/widgets/toggle.php:520
#: modules/floating-buttons/base/widget-contact-button-base.php:1678
#: modules/floating-buttons/base/widget-floating-bars-base.php:952
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1464
#: assets/js/ai-admin.js:11303 assets/js/ai-gutenberg.js:11521
#: assets/js/ai-media-library.js:11303
#: assets/js/ai-unify-product-images.js:11303 assets/js/ai.js:12764
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:14
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:246
msgid "Background"
msgstr "Sfondo"

#: includes/editor-templates/hotkeys.php:46 assets/js/editor.js:30670
#: assets/js/editor.js:32998 assets/js/editor.js:42621
#: assets/js/editor.js:43659
msgid "Paste"
msgstr "Incolla"

#: includes/widgets/image-carousel.php:364 includes/widgets/image.php:198
msgid "Custom URL"
msgstr "URL personalizzato"

#: includes/widgets/image-carousel.php:236
msgid "Arrows and Dots"
msgstr "Frecce e Punti"

#: includes/controls/groups/background.php:749
#: includes/controls/groups/flex-container.php:29
#: includes/widgets/image-carousel.php:545
#: modules/nested-tabs/widgets/nested-tabs.php:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:83
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:179
msgid "Direction"
msgstr "Direzione"

#: includes/widgets/image-carousel.php:587
#: includes/widgets/image-carousel.php:652
msgid "Inside"
msgstr "Interno"

#: includes/widgets/image-carousel.php:588
#: includes/widgets/image-carousel.php:651
msgid "Outside"
msgstr "Esterno"

#: includes/widgets/accordion.php:463 includes/widgets/divider.php:790
#: includes/widgets/divider.php:957 includes/widgets/image-carousel.php:771
#: includes/widgets/image-carousel.php:905
#: includes/widgets/image-gallery.php:440 includes/widgets/image.php:652
#: includes/widgets/rating.php:85 includes/widgets/social-icons.php:457
#: includes/widgets/star-rating.php:351 includes/widgets/toggle.php:487
#: modules/nested-accordion/widgets/nested-accordion.php:627
#: modules/nested-tabs/widgets/nested-tabs.php:915
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:10
msgid "Spacing"
msgstr "Spaziatura"

#: includes/elements/container.php:547 includes/widgets/audio.php:143
#: includes/widgets/image-carousel.php:427
msgid "Additional Options"
msgstr "Opzioni aggiuntive"

#: includes/elements/column.php:888 includes/elements/container.php:1832
#: includes/elements/section.php:1329 includes/widgets/common-base.php:849
#: includes/widgets/counter.php:188
#: modules/floating-buttons/base/widget-contact-button-base.php:1402
#: modules/floating-buttons/base/widget-contact-button-base.php:2785
#: modules/floating-buttons/base/widget-floating-bars-base.php:883
#: modules/nested-accordion/widgets/nested-accordion.php:367
msgid "Animation Duration"
msgstr "Durata dell'animazione"

#: includes/widgets/image-carousel.php:219
msgid "Image Stretch"
msgstr "Estendi l'Immagine"

#: includes/widgets/image-carousel.php:46
#: includes/widgets/image-carousel.php:146
#: includes/widgets/image-carousel.php:155
msgid "Image Carousel"
msgstr "Carosello d'immagini"

#: includes/widgets/image-carousel.php:534
msgid "Animation Speed"
msgstr "Velocità dell'animazione"

#: includes/widgets/video.php:557
msgid "Intro Byline"
msgstr "Firma dell'introduzione"

#: includes/controls/groups/image-size.php:303
msgid "You can crop the original image size to any custom size. You can also set a single value for height or width in order to keep the original size ratio."
msgstr "Puoi ritagliare l'immagine originale in qualsiasi misura personalizzata. Puoi anche indicare solo un valore per l'altezza o la larghezza così da mantenere le proporzioni originali."

#: includes/controls/groups/background.php:103 includes/widgets/video.php:45
#: includes/widgets/video.php:148 includes/widgets/video.php:751
msgid "Video"
msgstr "Video"

#: core/base/providers/social-network-provider.php:186
#: includes/widgets/video.php:160
msgid "Vimeo"
msgstr "Vimeo"

#: includes/controls/image-dimensions.php:81
msgid "The server does not have ImageMagick or GD installed and/or enabled! Any of these libraries are required for WordPress to be able to resize images. Please contact your server administrator to enable this before continuing."
msgstr "Il server non ha ImageMagick o GD installate e/o abilitate! Una di queste librerie è richiesta per permettere a WordPress di scalare le immagini, Contatta l'amministratore del tuo server per abilitarle prima di continuare."

#: includes/widgets/audio.php:251 includes/widgets/video.php:571
msgid "Controls Color"
msgstr "Colori dei controlli"

#: includes/widgets/video.php:543
msgid "Intro Portrait"
msgstr "Introduzione portrait"

#: includes/widgets/video.php:529
msgid "Intro Title"
msgstr "Titolo introduzione"

#: includes/widgets/video.php:397
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:82
msgid "Loop"
msgstr "Ciclico"

#: includes/widgets/video.php:352
msgid "Video Options"
msgstr "Opzioni video"

#: includes/controls/groups/background.php:525
msgid "Video Link"
msgstr "Link video"

#: includes/controls/media.php:362
msgctxt "Image Size Control"
msgid "Full"
msgstr "Pieno"

#: includes/widgets/image-carousel.php:517
msgid "Effect"
msgstr "Effetto"

#: includes/widgets/image-carousel.php:522
msgid "Fade"
msgstr "Sfuma"

#: includes/controls/gallery.php:92
msgid "Edit gallery"
msgstr "Modifica galleria"

#: includes/widgets/image-carousel.php:238
msgid "Dots"
msgstr "Punti"

#: includes/elements/column.php:183 includes/widgets/icon-box.php:265
#: includes/widgets/icon-list.php:572 includes/widgets/image-box.php:240
msgid "Vertical Alignment"
msgstr "Allineamento verticale"

#: includes/controls/groups/background.php:634
#: includes/widgets/image-carousel.php:504
msgid "Infinite Loop"
msgstr "Ciclo infinito"

#: includes/controls/groups/background.php:107
msgid "Slideshow"
msgstr "Slideshow"

#: includes/controls/groups/typography.php:321
msgctxt "Typography Control"
msgid "Width"
msgstr "Larghezza"

#: includes/elements/column.php:837 includes/elements/container.php:1794
#: includes/elements/section.php:1298 includes/widgets/common-base.php:811
#: modules/floating-buttons/base/widget-contact-button-base.php:3116
#: modules/floating-buttons/base/widget-floating-bars-base.php:1522
msgid "Add your custom class WITHOUT the dot. e.g: my-class"
msgstr "Aggiungi la tua classe personalizzata SENZA il punto es. my-class"

#: includes/widgets/accordion.php:243 includes/widgets/counter.php:242
#: includes/widgets/icon-box.php:207 includes/widgets/image-box.php:182
#: includes/widgets/progress.php:131 includes/widgets/toggle.php:246
#: modules/nested-accordion/widgets/nested-accordion.php:270
msgid "Title HTML Tag"
msgstr "Tag HTML Titolo"

#: includes/widgets/icon-box.php:172 includes/widgets/image-box.php:147
msgid "This is the heading"
msgstr "Questo è il titolo"

#: core/kits/documents/tabs/global-typography.php:200
msgid "The list of fonts used if the chosen font is not available."
msgstr "La lista dei font usati se il font scelto non è disponibile."

#: includes/editor-templates/panel-elements.php:14 assets/js/editor.js:19954
#: assets/js/editor.js:21851 assets/js/editor.js:22259
#: assets/js/editor.js:37655
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:19
#: assets/js/packages/editor-canvas/editor-canvas.js:2
#: assets/js/packages/editor-canvas/editor-canvas.strings.js:6
msgid "Elements"
msgstr "Elementi"

#: includes/widgets/wordpress.php:242
msgid "Form"
msgstr "Modulo"

#: core/admin/admin.php:343 core/admin/menu/main.php:75
#: core/common/modules/finder/categories/settings.php:29
#: core/dynamic-tags/base-tag.php:171 includes/editor-templates/panel.php:77
#: includes/managers/controls.php:339
#: includes/settings/admin-menu-items/admin-menu-item.php:28
#: includes/settings/settings.php:216
#: modules/atomic-widgets/elements/has-atomic-base.php:142
#: modules/usage/settings-reporter.php:13 assets/js/editor.js:7481
#: assets/js/editor.js:38588 assets/js/editor.js:48048
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:45
msgid "Settings"
msgstr "Impostazioni"

#: includes/controls/groups/typography.php:165
msgctxt "Typography Control"
msgid "Uppercase"
msgstr "Maiuscolo"

#: includes/controls/groups/typography.php:166
msgctxt "Typography Control"
msgid "Lowercase"
msgstr "Minuscolo"

#: includes/controls/groups/typography.php:180
msgctxt "Typography Control"
msgid "Oblique"
msgstr "Obliquo"

#: includes/controls/groups/typography.php:179
msgctxt "Typography Control"
msgid "Italic"
msgstr "Corsivo"

#: includes/controls/groups/typography.php:167
msgctxt "Typography Control"
msgid "Capitalize"
msgstr "Iniziali maiuscole"

#: modules/gutenberg/module.php:98
msgid "&#8592; Back to WordPress Editor"
msgstr "&#8592; Torna all'Editor di WordPress"

#: includes/base/element-base.php:1330 includes/controls/dimensions.php:83
#: includes/elements/column.php:743 includes/elements/container.php:1547
#: includes/elements/section.php:1199 includes/widgets/common-base.php:568
#: includes/widgets/common-base.php:569 includes/widgets/divider.php:482
#: includes/widgets/divider.php:778 includes/widgets/divider.php:944
#: includes/widgets/heading.php:278 includes/widgets/icon-box.php:251
#: includes/widgets/icon-box.php:312 includes/widgets/icon-list.php:271
#: includes/widgets/icon-list.php:553 includes/widgets/icon.php:197
#: includes/widgets/image-box.php:225 includes/widgets/image-box.php:280
#: includes/widgets/image-carousel.php:550
#: includes/widgets/image-carousel.php:856
#: includes/widgets/image-gallery.php:379 includes/widgets/image.php:271
#: includes/widgets/image.php:589 includes/widgets/social-icons.php:336
#: includes/widgets/star-rating.php:227 includes/widgets/tabs.php:434
#: includes/widgets/testimonial.php:255 includes/widgets/text-editor.php:270
#: includes/widgets/traits/button-trait.php:264
#: modules/floating-buttons/base/widget-contact-button-base.php:1140
#: modules/floating-buttons/base/widget-contact-button-base.php:2406
#: modules/floating-buttons/base/widget-contact-button-base.php:2948
#: modules/floating-buttons/base/widget-floating-bars-base.php:456
#: modules/floating-buttons/base/widget-floating-bars-base.php:1057
#: modules/shapes/widgets/text-path.php:185
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:23
#: assets/js/packages/editor-controls/editor-controls.strings.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:146
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:149
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:219
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:221
msgid "Right"
msgstr "Destra"

#: includes/base/element-base.php:1322 includes/controls/dimensions.php:85
#: includes/elements/column.php:735 includes/elements/container.php:1546
#: includes/elements/section.php:1191 includes/widgets/common-base.php:568
#: includes/widgets/common-base.php:569 includes/widgets/divider.php:474
#: includes/widgets/divider.php:770 includes/widgets/divider.php:936
#: includes/widgets/heading.php:270 includes/widgets/icon-box.php:243
#: includes/widgets/icon-box.php:304 includes/widgets/icon-list.php:263
#: includes/widgets/icon-list.php:545 includes/widgets/icon.php:189
#: includes/widgets/image-box.php:217 includes/widgets/image-box.php:272
#: includes/widgets/image-carousel.php:549
#: includes/widgets/image-carousel.php:848
#: includes/widgets/image-gallery.php:371 includes/widgets/image.php:263
#: includes/widgets/image.php:581 includes/widgets/social-icons.php:328
#: includes/widgets/star-rating.php:219 includes/widgets/tabs.php:426
#: includes/widgets/testimonial.php:247 includes/widgets/text-editor.php:262
#: includes/widgets/traits/button-trait.php:256
#: modules/floating-buttons/base/widget-contact-button-base.php:1136
#: modules/floating-buttons/base/widget-contact-button-base.php:2402
#: modules/floating-buttons/base/widget-contact-button-base.php:2940
#: modules/floating-buttons/base/widget-floating-bars-base.php:452
#: modules/floating-buttons/base/widget-floating-bars-base.php:1053
#: modules/shapes/widgets/text-path.php:177
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:22
#: assets/js/packages/editor-controls/editor-controls.strings.js:26
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:147
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:148
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:218
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:222
msgid "Left"
msgstr "Sinistra"

#: includes/fonts.php:71
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:247
msgid "System"
msgstr "Sistema"

#: includes/fonts.php:76
msgid "Google"
msgstr "Google"

#: includes/controls/groups/background.php:434
msgctxt "Background Control"
msgid "Fixed"
msgstr "Fisso"

#: includes/controls/groups/border.php:65 includes/widgets/divider.php:342
#: includes/widgets/icon-list.php:299 includes/widgets/star-rating.php:188
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:226
msgid "Solid"
msgstr "Solido"

#: includes/controls/groups/border.php:66 includes/widgets/divider.php:343
#: includes/widgets/icon-list.php:300
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:229
msgid "Double"
msgstr "Doppio"

#: includes/controls/groups/border.php:68 includes/widgets/divider.php:345
#: includes/widgets/icon-list.php:302
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:227
msgid "Dashed"
msgstr "Tratteggiato"

#: core/kits/documents/tabs/theme-style-typography.php:109
#: includes/elements/container.php:609 includes/widgets/audio.php:111
#: includes/widgets/heading.php:200 includes/widgets/icon-box.php:195
#: includes/widgets/icon-list.php:169 includes/widgets/icon.php:164
#: includes/widgets/image-box.php:170 includes/widgets/image-carousel.php:358
#: includes/widgets/image-carousel.php:372
#: includes/widgets/image-gallery.php:186 includes/widgets/image.php:192
#: includes/widgets/image.php:209 includes/widgets/social-icons.php:194
#: includes/widgets/testimonial.php:204
#: includes/widgets/traits/button-trait.php:99 includes/widgets/video.php:172
#: includes/widgets/video.php:197 includes/widgets/video.php:221
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:75
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:108
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:79
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:74
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:68
#: modules/atomic-widgets/elements/div-block/div-block.php:103
#: modules/floating-buttons/base/widget-contact-button-base.php:418
#: modules/floating-buttons/base/widget-contact-button-base.php:933
#: modules/floating-buttons/base/widget-contact-button-base.php:1061
#: modules/floating-buttons/base/widget-floating-bars-base.php:164
#: modules/floating-buttons/base/widget-floating-bars-base.php:350
#: modules/floating-buttons/base/widget-floating-bars-base.php:568
#: modules/link-in-bio/base/widget-link-in-bio-base.php:264
#: modules/link-in-bio/base/widget-link-in-bio-base.php:407
#: modules/link-in-bio/base/widget-link-in-bio-base.php:643
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1099
#: modules/shapes/widgets/text-path.php:158
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:28
msgid "Link"
msgstr "Link"

#: core/kits/documents/tabs/settings-background.php:79
#: core/kits/documents/tabs/settings-lightbox.php:99
#: core/kits/documents/tabs/settings-lightbox.php:116
#: includes/controls/animation.php:155 includes/controls/groups/border.php:64
#: includes/controls/groups/flex-item.php:130
#: includes/controls/groups/typography.php:193
#: includes/controls/hover-animation.php:129 includes/controls/icons.php:108
#: includes/controls/icons.php:194 includes/widgets/divider.php:501
#: includes/widgets/image-carousel.php:239
#: includes/widgets/image-carousel.php:362
#: includes/widgets/image-carousel.php:414
#: includes/widgets/image-gallery.php:174
#: includes/widgets/image-gallery.php:192 includes/widgets/image.php:161
#: includes/widgets/image.php:196 includes/widgets/video.php:601
#: modules/nested-tabs/widgets/nested-tabs.php:406 assets/js/ai-admin.js:11276
#: assets/js/ai-admin.js:11282 assets/js/ai-admin.js:11294
#: assets/js/ai-admin.js:11305 assets/js/ai-admin.js:11316
#: assets/js/ai-admin.js:11327 assets/js/ai-admin.js:11343
#: assets/js/ai-gutenberg.js:11494 assets/js/ai-gutenberg.js:11500
#: assets/js/ai-gutenberg.js:11512 assets/js/ai-gutenberg.js:11523
#: assets/js/ai-gutenberg.js:11534 assets/js/ai-gutenberg.js:11545
#: assets/js/ai-gutenberg.js:11561 assets/js/ai-media-library.js:11276
#: assets/js/ai-media-library.js:11282 assets/js/ai-media-library.js:11294
#: assets/js/ai-media-library.js:11305 assets/js/ai-media-library.js:11316
#: assets/js/ai-media-library.js:11327 assets/js/ai-media-library.js:11343
#: assets/js/ai-unify-product-images.js:11276
#: assets/js/ai-unify-product-images.js:11282
#: assets/js/ai-unify-product-images.js:11294
#: assets/js/ai-unify-product-images.js:11305
#: assets/js/ai-unify-product-images.js:11316
#: assets/js/ai-unify-product-images.js:11327
#: assets/js/ai-unify-product-images.js:11343 assets/js/ai.js:12737
#: assets/js/ai.js:12743 assets/js/ai.js:12755 assets/js/ai.js:12766
#: assets/js/ai.js:12777 assets/js/ai.js:12788 assets/js/ai.js:12804
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:78
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:87
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:134
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:192
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:225
msgid "None"
msgstr "Nessuno"

#: includes/controls/groups/typography.php:106
msgctxt "Typography Control"
msgid "Family"
msgstr "Famiglia"

#: includes/controls/groups/typography.php:138
#: includes/controls/groups/typography.php:299
msgctxt "Typography Control"
msgid "Weight"
msgstr "Peso"

#: includes/controls/groups/typography.php:160
msgctxt "Typography Control"
msgid "Transform"
msgstr "Trasformazione"

#: includes/controls/groups/typography.php:173
msgctxt "Typography Control"
msgid "Style"
msgstr "Stile"

#: core/admin/admin.php:242
#: core/editor/loader/v1/templates/editor-body-v1-view.php:23
#: core/editor/loader/v2/templates/editor-body-v2-view.php:23
#: includes/editor-templates/templates.php:54 modules/gutenberg/module.php:123
#: assets/js/ai-admin.js:1735 assets/js/ai-gutenberg.js:1873
#: assets/js/ai-media-library.js:1735 assets/js/ai-unify-product-images.js:1735
#: assets/js/ai.js:2518 assets/js/app-packages.js:3976 assets/js/app.js:1113
msgid "Loading"
msgstr "Caricamento"

#: includes/editor-templates/panel.php:66
#: includes/editor-templates/panel.php:67
msgid "Menu"
msgstr "Menu"

#: includes/editor-templates/hotkeys.php:165
#: includes/editor-templates/panel.php:88
msgid "Responsive Mode"
msgstr "Modalità Responsive"

#: core/base/traits/shared-widget-controls-trait.php:270
#: includes/base/element-base.php:1386 includes/editor-templates/panel.php:272
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:5305
#: assets/js/packages/editor-responsive/editor-responsive.js:2
#: assets/js/packages/editor-responsive/editor-responsive.strings.js:1
msgid "Desktop"
msgstr "Desktop"

#: core/breakpoints/manager.php:334
msgid "Laptop"
msgstr "Laptop"

#. Plugin Name of the plugin
#: elementor.php app/view.php:23 core/admin/admin.php:292
#: core/admin/admin.php:409 core/admin/admin.php:487
#: core/admin/menu/main.php:17 core/admin/menu/main.php:18
#: core/documents-manager.php:384 core/upgrade/custom-tasks-manager.php:29
#: core/upgrade/manager.php:47 includes/editor-templates/navigator.php:94
#: includes/editor-templates/panel-elements.php:100
#: includes/editor-templates/panel.php:197
#: includes/editor-templates/templates.php:220 includes/plugin.php:868
#: includes/settings/admin-menu-items/admin-menu-item.php:29
#: includes/settings/settings.php:91 includes/settings/settings.php:92
#: includes/settings/settings.php:487 modules/compatibility-tag/module.php:36
#: modules/history/views/history-panel-template.php:23
#: modules/history/views/revisions-panel-template.php:38
#: assets/js/app-packages.js:319 assets/js/app.js:387
msgid "Elementor"
msgstr "Elementor"

#: core/breakpoints/manager.php:319
msgid "Mobile Landscape"
msgstr "Mobile Landscape"

#: core/breakpoints/manager.php:314
msgid "Mobile Portrait"
msgstr "Mobile Portrait"

#: core/admin/admin.php:621 core/admin/menu/main.php:41 assets/js/app.js:6155
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:2
msgid "Help"
msgstr "Aiuto"

#: includes/editor-templates/hotkeys.php:78
#: includes/editor-templates/templates.php:23
#: includes/editor-templates/templates.php:477
#: includes/editor-templates/templates.php:489 assets/js/e-home-screen.js:244
#: assets/js/editor.js:8560 assets/js/editor.js:45521
#: assets/js/element-manager-admin.js:785
#: assets/js/kit-elements-defaults-editor.js:597
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:21
#: assets/js/packages/editor-variables/editor-variables.strings.js:33
msgid "Save"
msgstr "Salva"

#: core/editor/loader/v1/templates/editor-body-v1-view.php:31
#: core/editor/loader/v2/templates/editor-body-v2-view.php:33
#: includes/editor-templates/templates.php:296 assets/js/ai-admin.js:7558
#: assets/js/ai-gutenberg.js:7776 assets/js/ai-layout.js:3049
#: assets/js/ai-media-library.js:7558 assets/js/ai-unify-product-images.js:7558
#: assets/js/ai.js:9019 assets/js/editor.js:28194
msgid "Preview"
msgstr "Anteprima"

#: includes/controls/groups/typography.php:113
msgctxt "Typography Control"
msgid "Size"
msgstr "Dimensione"

#: includes/controls/groups/background.php:428
msgctxt "Background Control"
msgid "Attachment"
msgstr "Allegato"

#: includes/controls/image-dimensions.php:102
#: includes/editor-templates/panel.php:164
#: includes/editor-templates/templates.php:446
#: modules/history/views/revisions-panel-template.php:14
#: assets/js/editor.js:8184 assets/js/editor.js:38669
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:851
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:879
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:1330
msgid "Apply"
msgstr "Applica"

#: modules/floating-buttons/base/widget-contact-button-base.php:1209
#: modules/floating-buttons/base/widget-contact-button-base.php:1261
#: modules/floating-buttons/base/widget-contact-button-base.php:1348
#: modules/floating-buttons/base/widget-contact-button-base.php:1557
#: modules/floating-buttons/base/widget-contact-button-base.php:1723
#: modules/floating-buttons/base/widget-contact-button-base.php:2613
#: modules/floating-buttons/base/widget-contact-button-base.php:2682
#: assets/js/styleguide-app.77392704cadf8bc1ca69.bundle.js:443
msgid "Colors"
msgstr "Colori"

#: includes/controls/groups/grid-container.php:42
#: includes/widgets/image-gallery.php:160 includes/widgets/social-icons.php:295
#: includes/widgets/text-editor.php:177
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:115
msgid "Columns"
msgstr "Colonne"

#: includes/managers/controls.php:335 includes/widgets/divider.php:385
#: includes/widgets/icon-list.php:296 assets/js/ai-admin.js:10491
#: assets/js/ai-gutenberg.js:10709 assets/js/ai-media-library.js:10491
#: assets/js/ai-unify-product-images.js:10491 assets/js/ai.js:11952
#: assets/js/editor.js:7484 assets/js/editor.js:37460
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:17
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:25
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:2
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:20
msgid "Style"
msgstr "Stile"

#: core/kits/manager.php:139 includes/controls/groups/background.php:329
#: includes/controls/groups/background.php:487
#: includes/controls/groups/flex-item.php:24
#: includes/controls/groups/flex-item.php:93
#: includes/controls/groups/flex-item.php:142
#: includes/controls/groups/image-size.php:383
#: includes/elements/container.php:1436 includes/elements/container.php:1480
#: includes/elements/section.php:293 includes/maintenance-mode.php:238
#: includes/widgets/common-base.php:344 includes/widgets/common-base.php:416
#: includes/widgets/common-base.php:460 includes/widgets/common-base.php:1158
#: includes/widgets/common-base.php:1215
#: includes/widgets/image-carousel.php:775
#: includes/widgets/image-gallery.php:290 includes/widgets/social-icons.php:213
#: includes/widgets/social-icons.php:366
#: modules/floating-buttons/base/widget-contact-button-base.php:1214
#: modules/floating-buttons/base/widget-contact-button-base.php:1266
#: modules/floating-buttons/base/widget-contact-button-base.php:1353
#: modules/floating-buttons/base/widget-contact-button-base.php:1562
#: modules/floating-buttons/base/widget-contact-button-base.php:1728
#: modules/floating-buttons/base/widget-contact-button-base.php:2292
#: modules/floating-buttons/base/widget-contact-button-base.php:2618
#: modules/floating-buttons/base/widget-contact-button-base.php:2687
#: modules/floating-buttons/base/widget-contact-button-base.php:2818
#: modules/shapes/module.php:52 assets/js/editor.js:46071
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:15
#: assets/js/packages/editor-controls/editor-controls.strings.js:81
#: assets/js/packages/editor-controls/editor-controls.strings.js:151
#: assets/js/packages/editor-controls/editor-controls.strings.js:167
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:170
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:177
msgid "Custom"
msgstr "Personalizzato"

#: core/kits/documents/tabs/theme-style-buttons.php:91
#: core/kits/documents/tabs/theme-style-form-fields.php:117
#: core/kits/documents/tabs/theme-style-images.php:63
#: core/kits/documents/tabs/theme-style-typography.php:119
#: includes/base/element-base.php:883
#: includes/controls/groups/typography.php:152
#: includes/controls/groups/typography.php:168
#: includes/controls/groups/typography.php:178 includes/elements/column.php:286
#: includes/elements/column.php:401 includes/elements/column.php:450
#: includes/elements/column.php:557 includes/elements/column.php:893
#: includes/elements/container.php:656 includes/elements/container.php:785
#: includes/elements/container.php:859 includes/elements/container.php:1004
#: includes/elements/container.php:1837 includes/elements/section.php:543
#: includes/elements/section.php:654 includes/elements/section.php:718
#: includes/elements/section.php:841 includes/elements/section.php:1334
#: includes/widgets/alert.php:412 includes/widgets/common-base.php:854
#: includes/widgets/common-base.php:901 includes/widgets/common-base.php:976
#: includes/widgets/google-maps.php:221 includes/widgets/heading.php:327
#: includes/widgets/heading.php:359 includes/widgets/icon-box.php:399
#: includes/widgets/icon-box.php:670 includes/widgets/icon-list.php:425
#: includes/widgets/icon-list.php:654 includes/widgets/icon.php:213
#: includes/widgets/image-box.php:489 includes/widgets/image-box.php:638
#: includes/widgets/image.php:434 includes/widgets/text-editor.php:336
#: includes/widgets/traits/button-trait.php:340
#: modules/floating-buttons/base/widget-contact-button-base.php:1201
#: modules/floating-buttons/base/widget-contact-button-base.php:1407
#: modules/floating-buttons/base/widget-contact-button-base.php:1979
#: modules/floating-buttons/base/widget-contact-button-base.php:2474
#: modules/floating-buttons/base/widget-contact-button-base.php:2605
#: modules/floating-buttons/base/widget-contact-button-base.php:2790
#: modules/floating-buttons/base/widget-floating-bars-base.php:656
#: modules/floating-buttons/base/widget-floating-bars-base.php:888
#: modules/floating-buttons/base/widget-floating-bars-base.php:1374
#: modules/nested-accordion/widgets/nested-accordion.php:671
#: modules/nested-accordion/widgets/nested-accordion.php:721
#: modules/nested-tabs/widgets/nested-tabs.php:493
#: modules/nested-tabs/widgets/nested-tabs.php:737
#: modules/nested-tabs/widgets/nested-tabs.php:937
#: modules/shapes/widgets/text-path.php:409
#: modules/shapes/widgets/text-path.php:501
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:110
msgid "Normal"
msgstr "Normale"

#: includes/managers/controls.php:334 includes/widgets/accordion.php:145
#: includes/widgets/accordion.php:488 includes/widgets/alert.php:148
#: includes/widgets/icon-box.php:624 includes/widgets/image-box.php:592
#: includes/widgets/tabs.php:141 includes/widgets/tabs.php:450
#: includes/widgets/testimonial.php:139 includes/widgets/testimonial.php:272
#: includes/widgets/toggle.php:145 includes/widgets/toggle.php:512
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:63
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:65
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:63
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:62
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:57
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:72
#: modules/nested-accordion/widgets/nested-accordion.php:492
#: modules/nested-tabs/widgets/nested-tabs.php:990 assets/js/app.js:4615
#: assets/js/app.js:6326 assets/js/app.js:11350 assets/js/app.js:11859
#: assets/js/editor.js:37457
msgid "Content"
msgstr "Contenuto"

#: includes/editor-templates/hotkeys.php:120
#: includes/editor-templates/navigator.php:35
#: includes/editor-templates/panel.php:81 includes/elements/section.php:511
#: includes/elements/section.php:519 assets/js/editor.js:31919
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:8
msgid "Structure"
msgstr "Struttura"

#: includes/elements/section.php:78 modules/library/documents/section.php:43
#: assets/js/container-converter.js:95 assets/js/editor.js:8483
msgid "Section"
msgstr "Sezione"

#: includes/controls/gaps.php:57
#: includes/controls/groups/grid-container.php:119
#: includes/elements/column.php:61
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:47
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:181
msgid "Column"
msgstr "Colonna"

#: includes/controls/icons.php:80 includes/controls/media.php:238
msgid "Add"
msgstr "Aggiungi"

#: core/common/modules/finder/categories/settings.php:59
#: core/dynamic-tags/tag.php:88 includes/elements/column.php:763
#: includes/elements/section.php:1219 includes/managers/controls.php:336
#: includes/settings/settings.php:329 includes/settings/settings.php:332
#: modules/floating-buttons/base/widget-contact-button-base.php:2921
#: modules/floating-buttons/base/widget-floating-bars-base.php:1419
#: modules/floating-buttons/module.php:79
#: modules/floating-buttons/module.php:84 assets/js/editor.js:7487
#: assets/js/editor.js:37463
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3704
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:3708
#: assets/js/onboarding.04d189eccf40ae88f85d.bundle.js:1386
msgid "Advanced"
msgstr "Avanzato"

#: includes/base/element-base.php:1326 includes/base/element-base.php:1354
#: includes/controls/groups/flex-container.php:101
#: includes/controls/groups/flex-container.php:137
#: includes/controls/groups/flex-item.php:59
#: includes/controls/groups/grid-container.php:139
#: includes/controls/groups/grid-container.php:167
#: includes/elements/column.php:217 includes/elements/column.php:739
#: includes/elements/section.php:1195 includes/widgets/common-base.php:522
#: includes/widgets/counter.php:322 includes/widgets/counter.php:396
#: includes/widgets/counter.php:432 includes/widgets/divider.php:478
#: includes/widgets/divider.php:774 includes/widgets/divider.php:940
#: includes/widgets/heading.php:274 includes/widgets/icon-box.php:308
#: includes/widgets/icon-list.php:267 includes/widgets/icon-list.php:549
#: includes/widgets/icon-list.php:580 includes/widgets/icon.php:193
#: includes/widgets/image-box.php:276 includes/widgets/image-carousel.php:751
#: includes/widgets/image-carousel.php:852
#: includes/widgets/image-gallery.php:375 includes/widgets/image.php:267
#: includes/widgets/image.php:585 includes/widgets/rating.php:207
#: includes/widgets/social-icons.php:332 includes/widgets/star-rating.php:223
#: includes/widgets/tabs.php:213 includes/widgets/tabs.php:243
#: includes/widgets/tabs.php:430 includes/widgets/testimonial.php:251
#: includes/widgets/text-editor.php:266
#: includes/widgets/traits/button-trait.php:260
#: includes/widgets/traits/button-trait.php:292 includes/widgets/video.php:978
#: modules/floating-buttons/base/widget-contact-button-base.php:2944
#: modules/floating-buttons/base/widget-floating-bars-base.php:1193
#: modules/nested-accordion/widgets/nested-accordion.php:177
#: modules/nested-tabs/widgets/nested-tabs.php:238
#: modules/nested-tabs/widgets/nested-tabs.php:280
#: modules/nested-tabs/widgets/nested-tabs.php:350
#: modules/shapes/widgets/text-path.php:181
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:94
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:161
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:197
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:202
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:207
msgid "Center"
msgstr "Centra"

#: core/document-types/page-base.php:132 includes/elements/column.php:772
#: includes/elements/container.php:1376 includes/elements/section.php:1227
#: includes/widgets/common-base.php:310
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:116
msgid "Margin"
msgstr "Margine"

#: includes/elements/column.php:827 includes/elements/container.php:1784
#: includes/elements/section.php:1288 includes/widgets/common-base.php:802
#: modules/floating-buttons/base/widget-contact-button-base.php:3107
#: modules/floating-buttons/base/widget-floating-bars-base.php:1513
msgid "CSS Classes"
msgstr "Classi CSS"

#: includes/widgets/html.php:42
msgid "HTML"
msgstr "HTML"

#: includes/widgets/html.php:97 includes/widgets/html.php:104
msgid "HTML Code"
msgstr "Codice HTML"

#. Author of the plugin
#: elementor.php
msgid "Elementor.com"
msgstr "Elementor.com"

#: includes/widgets/progress.php:188 includes/widgets/progress.php:297
msgid "Percentage"
msgstr "Percentuale"

#: includes/widgets/divider.php:898 includes/widgets/icon-box.php:406
#: includes/widgets/icon-box.php:447 includes/widgets/icon.php:220
#: includes/widgets/icon.php:263 includes/widgets/social-icons.php:221
#: includes/widgets/social-icons.php:374 includes/widgets/social-icons.php:531
#: includes/widgets/text-editor.php:436
msgid "Primary Color"
msgstr "Colore principale"

#: includes/widgets/divider.php:915 includes/widgets/icon-box.php:422
#: includes/widgets/icon-box.php:464 includes/widgets/icon.php:237
#: includes/widgets/icon.php:277 includes/widgets/social-icons.php:235
#: includes/widgets/social-icons.php:388 includes/widgets/social-icons.php:546
#: includes/widgets/text-editor.php:451
msgid "Secondary Color"
msgstr "Colore secondario"

#: core/base/traits/shared-widget-controls-trait.php:179
#: includes/controls/groups/border.php:77 includes/widgets/accordion.php:281
#: includes/widgets/divider.php:1002 includes/widgets/icon-box.php:591
#: includes/widgets/icon.php:392 includes/widgets/tabs.php:311
#: includes/widgets/text-editor.php:551 includes/widgets/toggle.php:284
#: modules/floating-buttons/base/widget-floating-bars-base.php:777
#: modules/nested-accordion/widgets/nested-accordion.php:517
#: modules/nested-tabs/widgets/nested-tabs.php:525
#: modules/nested-tabs/widgets/nested-tabs.php:583
#: modules/nested-tabs/widgets/nested-tabs.php:667
#: modules/nested-tabs/widgets/nested-tabs.php:1019
msgid "Border Width"
msgstr "Spessore bordi"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1651
msgid "Image Size"
msgstr "Dimensione immagine"

#: modules/history/views/revisions-panel-template.php:11
#: assets/js/editor.js:45522
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:27
msgid "Discard"
msgstr "Abbandona"

#: core/kits/documents/tabs/theme-style-typography.php:18
#: core/kits/documents/tabs/theme-style-typography.php:37
#: includes/controls/groups/typography.php:436 includes/elements/column.php:674
#: includes/elements/section.php:1131 assets/js/editor-modules.js:1431
#: assets/js/editor.js:43331
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:13
msgid "Typography"
msgstr "Tipografia"

#: core/settings/editor-preferences/model.php:90
#: includes/controls/groups/background.php:499
#: includes/controls/image-dimensions.php:95
#: includes/elements/container.php:405 includes/elements/container.php:1239
#: includes/elements/section.php:263 includes/elements/section.php:1022
#: includes/widgets/common-base.php:337 includes/widgets/divider.php:443
#: includes/widgets/icon-list.php:343 includes/widgets/image-box.php:362
#: includes/widgets/image.php:284
#: modules/floating-buttons/base/widget-contact-button-base.php:2842
#: modules/nested-tabs/widgets/nested-tabs.php:312
#: modules/shapes/widgets/text-path.php:540
#: modules/shapes/widgets/text-path.php:611
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:118
msgid "Width"
msgstr "Larghezza"

#: includes/elements/section.php:289
msgid "Narrow"
msgstr "Stretto"

#: includes/elements/section.php:291
msgid "Wide"
msgstr "Largo"

#: includes/controls/image-dimensions.php:100
#: includes/elements/container.php:1273 includes/elements/section.php:321
#: includes/elements/section.php:361 includes/elements/section.php:1056
#: includes/widgets/google-maps.php:192 includes/widgets/icon-list.php:362
#: includes/widgets/image-box.php:390 includes/widgets/image.php:354
#: includes/widgets/progress.php:331
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:119
msgid "Height"
msgstr "Altezza"

#: includes/controls/groups/flex-container.php:214
#: includes/controls/groups/grid-container.php:196
#: includes/controls/groups/grid-container.php:236
#: includes/elements/column.php:189 includes/elements/section.php:407
#: includes/elements/section.php:426 includes/widgets/counter.php:351
#: includes/widgets/icon-box.php:273 includes/widgets/image-box.php:248
#: modules/floating-buttons/base/widget-contact-button-base.php:2998
msgid "Middle"
msgstr "Centrale"

#: core/settings/editor-preferences/model.php:195
#: includes/widgets/image-carousel.php:232
#: includes/widgets/image-carousel.php:560
msgid "Navigation"
msgstr "Navigazione"

#: includes/managers/elements.php:351
msgid "WordPress"
msgstr "WordPress"

#: core/kits/documents/tabs/global-colors.php:77
#: core/kits/documents/tabs/global-typography.php:128
msgid "Primary"
msgstr "Principale"

#: core/kits/documents/tabs/global-colors.php:82
#: core/kits/documents/tabs/global-typography.php:135
msgid "Secondary"
msgstr "Secondario"

#: includes/editor-templates/templates.php:194
#: includes/widgets/testimonial.php:174 includes/widgets/testimonial.php:370
#: modules/floating-buttons/base/widget-contact-button-base.php:121
#: modules/floating-buttons/base/widget-contact-button-base.php:209
#: modules/floating-buttons/base/widget-contact-button-base.php:653
#: modules/floating-buttons/base/widget-contact-button-base.php:1737
#: assets/js/app.js:7717
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:68
msgid "Name"
msgstr "Nome"

#: includes/controls/groups/flex-container.php:145
#: includes/controls/groups/flex-item.php:67
#: includes/controls/groups/grid-container.php:147
#: includes/controls/groups/grid-container.php:175
#: includes/elements/section.php:405 includes/widgets/counter.php:404
#: includes/widgets/tabs.php:221 includes/widgets/tabs.php:251
#: includes/widgets/traits/button-trait.php:268
#: modules/floating-buttons/base/widget-floating-bars-base.php:1201
#: modules/nested-accordion/widgets/nested-accordion.php:185
#: modules/nested-tabs/widgets/nested-tabs.php:246
#: modules/nested-tabs/widgets/nested-tabs.php:288
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:199
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:204
msgid "Stretch"
msgstr "Estendi"

#: core/kits/documents/tabs/global-colors.php:87
#: core/kits/documents/tabs/global-typography.php:142
#: includes/widgets/divider.php:505 includes/widgets/divider.php:523
#: includes/widgets/divider.php:722 includes/widgets/icon-list.php:142
#: includes/widgets/icon-list.php:625
#: includes/widgets/traits/button-trait.php:58
#: modules/floating-buttons/base/widget-contact-button-base.php:145
#: modules/floating-buttons/base/widget-contact-button-base.php:1044
#: modules/floating-buttons/base/widget-floating-bars-base.php:63
#: modules/floating-buttons/base/widget-floating-bars-base.php:151
#: modules/floating-buttons/base/widget-floating-bars-base.php:337
#: modules/floating-buttons/base/widget-floating-bars-base.php:1353
#: modules/link-in-bio/base/widget-link-in-bio-base.php:328
#: modules/link-in-bio/base/widget-link-in-bio-base.php:585
#: modules/shapes/widgets/text-path.php:111
#: modules/shapes/widgets/text-path.php:304 assets/js/ai-admin.js:3592
#: assets/js/ai-gutenberg.js:3730 assets/js/ai-media-library.js:3592
#: assets/js/ai-unify-product-images.js:3592 assets/js/ai.js:4408
msgid "Text"
msgstr "Testo"

#: core/base/traits/shared-widget-controls-trait.php:158
#: core/settings/editor-preferences/model.php:118
#: core/settings/editor-preferences/model.php:130
#: core/settings/editor-preferences/model.php:141
#: core/settings/editor-preferences/model.php:163
#: core/settings/editor-preferences/model.php:186
#: includes/controls/switcher.php:74 includes/managers/icons.php:470
#: includes/widgets/audio.php:134 includes/widgets/image-carousel.php:224
#: includes/widgets/image-carousel.php:398
#: includes/widgets/image-carousel.php:445
#: includes/widgets/image-carousel.php:458
#: includes/widgets/image-carousel.php:475
#: includes/widgets/image-carousel.php:506
#: includes/widgets/image-gallery.php:211 includes/widgets/image.php:236
#: modules/floating-buttons/base/widget-contact-button-base.php:3047
#: modules/floating-buttons/base/widget-floating-bars-base.php:763
#: modules/floating-buttons/base/widget-floating-bars-base.php:1455
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1556
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1614
#: modules/nested-accordion/widgets/nested-accordion.php:309
#: modules/styleguide/module.php:132 assets/js/app.js:9919
msgid "Yes"
msgstr "Sì"

#: core/base/traits/shared-widget-controls-trait.php:159
#: core/settings/editor-preferences/model.php:119
#: core/settings/editor-preferences/model.php:131
#: core/settings/editor-preferences/model.php:142
#: core/settings/editor-preferences/model.php:164
#: core/settings/editor-preferences/model.php:187
#: includes/controls/switcher.php:73 includes/managers/icons.php:469
#: includes/widgets/audio.php:135 includes/widgets/image-carousel.php:223
#: includes/widgets/image-carousel.php:399
#: includes/widgets/image-carousel.php:446
#: includes/widgets/image-carousel.php:459
#: includes/widgets/image-carousel.php:476
#: includes/widgets/image-carousel.php:507
#: includes/widgets/image-gallery.php:212 includes/widgets/image.php:237
#: modules/floating-buttons/base/widget-contact-button-base.php:3048
#: modules/floating-buttons/base/widget-floating-bars-base.php:764
#: modules/floating-buttons/base/widget-floating-bars-base.php:1456
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1557
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1615
#: modules/nested-accordion/widgets/nested-accordion.php:310
#: modules/styleguide/module.php:131
msgid "No"
msgstr "No"

#: modules/system-info/module.php:166
msgid "You can copy the below info as simple text with Ctrl+C / Ctrl+V:"
msgstr "Puoi copiare le informazioni sottostanti come semplice testo usando Ctrl+C / Ctrl+V:"

#: core/admin/admin-notices.php:238
msgid "Sure! I'd love to help"
msgstr "Certamente! Voglio aiutarvi"

#: includes/widgets/divider.php:832 includes/widgets/icon-box.php:130
#: includes/widgets/icon.php:131 includes/widgets/text-editor.php:421
#: assets/js/editor.js:10627
msgid "View"
msgstr "Visualizza"

#: includes/widgets/alert.php:46 includes/widgets/alert.php:111
#: includes/widgets/alert.php:210
msgid "Alert"
msgstr "Avviso"

#: includes/editor-templates/templates.php:198
#: includes/elements/container.php:1196 includes/elements/section.php:979
#: includes/template-library/sources/local.php:1702
#: includes/widgets/alert.php:118 includes/widgets/progress.php:168
#: includes/widgets/traits/button-trait.php:67
#: modules/floating-buttons/base/widget-floating-bars-base.php:563
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1094
msgid "Type"
msgstr "Tipo"

#: includes/widgets/alert.php:122 includes/widgets/progress.php:172
#: includes/widgets/traits/button-trait.php:72 assets/js/app-packages.js:4364
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:4389
msgid "Info"
msgstr "Info"

#: includes/widgets/alert.php:123 includes/widgets/progress.php:173
#: includes/widgets/traits/button-trait.php:73
msgid "Success"
msgstr "Successo"

#: includes/widgets/alert.php:124 includes/widgets/progress.php:174
#: includes/widgets/traits/button-trait.php:74
msgid "Warning"
msgstr "Avvertimento"

#: includes/widgets/alert.php:125 includes/widgets/progress.php:175
#: includes/widgets/traits/button-trait.php:75
msgid "Danger"
msgstr "Pericolo"

#: core/kits/documents/tabs/settings-lightbox.php:103
#: core/kits/documents/tabs/settings-lightbox.php:113
#: core/kits/documents/tabs/settings-lightbox.php:120
#: includes/compatibility.php:163 includes/widgets/alert.php:302
#: includes/widgets/icon-box.php:181 includes/widgets/icon-box.php:736
#: includes/widgets/image-box.php:156 includes/widgets/image-box.php:704
#: includes/widgets/image-carousel.php:417
#: modules/floating-buttons/base/widget-contact-button-base.php:2342
#: modules/link-in-bio/base/widget-link-in-bio-base.php:895
#: modules/link-in-bio/base/widget-link-in-bio-base.php:900
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1351
msgid "Description"
msgstr "Descrizione"

#: includes/base/element-base.php:1400
#: includes/controls/groups/grid-container.php:33
#: includes/widgets/alert.php:163 includes/widgets/audio.php:163
#: includes/widgets/audio.php:174 includes/widgets/audio.php:185
#: includes/widgets/audio.php:196 includes/widgets/audio.php:210
#: includes/widgets/audio.php:221 includes/widgets/audio.php:232
#: includes/widgets/audio.php:243 includes/widgets/counter.php:202
#: includes/widgets/progress.php:156 includes/widgets/progress.php:206
#: includes/widgets/video.php:412 includes/widgets/video.php:427
#: includes/widgets/video.php:454 includes/widgets/video.php:532
#: includes/widgets/video.php:546 includes/widgets/video.php:560
#: includes/widgets/video.php:586 includes/widgets/video.php:645
#: includes/widgets/video.php:686
#: modules/floating-buttons/base/widget-contact-button-base.php:518
#: modules/floating-buttons/base/widget-contact-button-base.php:628
#: modules/floating-buttons/base/widget-contact-button-base.php:693
#: modules/floating-buttons/base/widget-contact-button-base.php:2527
#: modules/floating-buttons/base/widget-floating-bars-base.php:230
#: modules/floating-buttons/base/widget-floating-bars-base.php:297
#: assets/js/element-manager-admin.js:449
#: assets/js/packages/editor-controls/editor-controls.js:28
#: assets/js/packages/editor-controls/editor-controls.strings.js:84
msgid "Show"
msgstr "Mostra"

#: includes/base/element-base.php:1399
#: includes/controls/groups/grid-container.php:34
#: includes/widgets/alert.php:164 includes/widgets/audio.php:162
#: includes/widgets/audio.php:173 includes/widgets/audio.php:184
#: includes/widgets/audio.php:195 includes/widgets/audio.php:209
#: includes/widgets/audio.php:220 includes/widgets/audio.php:231
#: includes/widgets/audio.php:242 includes/widgets/counter.php:203
#: includes/widgets/progress.php:157 includes/widgets/progress.php:207
#: includes/widgets/video.php:411 includes/widgets/video.php:426
#: includes/widgets/video.php:453 includes/widgets/video.php:531
#: includes/widgets/video.php:545 includes/widgets/video.php:559
#: includes/widgets/video.php:585 includes/widgets/video.php:644
#: includes/widgets/video.php:685
#: modules/floating-buttons/base/widget-contact-button-base.php:519
#: modules/floating-buttons/base/widget-contact-button-base.php:629
#: modules/floating-buttons/base/widget-contact-button-base.php:694
#: modules/floating-buttons/base/widget-contact-button-base.php:2528
#: modules/floating-buttons/base/widget-floating-bars-base.php:231
#: modules/floating-buttons/base/widget-floating-bars-base.php:298
#: assets/js/packages/editor-controls/editor-controls.js:28
#: assets/js/packages/editor-controls/editor-controls.strings.js:85
msgid "Hide"
msgstr "Nascondi"

#: core/base/document.php:1980
#: core/kits/documents/tabs/settings-lightbox.php:96
#: core/kits/documents/tabs/settings-lightbox.php:100
#: core/kits/documents/tabs/settings-lightbox.php:117
#: includes/elements/column.php:127 includes/elements/section.php:240
#: includes/widgets/accordion.php:132 includes/widgets/accordion.php:318
#: includes/widgets/alert.php:134 includes/widgets/alert.php:262
#: includes/widgets/common-base.php:301 includes/widgets/counter.php:228
#: includes/widgets/counter.php:542 includes/widgets/heading.php:184
#: includes/widgets/icon-box.php:167 includes/widgets/icon-box.php:632
#: includes/widgets/image-box.php:142 includes/widgets/image-box.php:600
#: includes/widgets/image-carousel.php:415 includes/widgets/progress.php:117
#: includes/widgets/progress.php:244 includes/widgets/star-rating.php:203
#: includes/widgets/star-rating.php:247 includes/widgets/tabs.php:127
#: includes/widgets/tabs.php:357 includes/widgets/testimonial.php:189
#: includes/widgets/testimonial.php:415 includes/widgets/toggle.php:132
#: includes/widgets/toggle.php:350
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:68
#: modules/floating-buttons/base/widget-contact-button-base.php:129
#: modules/floating-buttons/base/widget-contact-button-base.php:210
#: modules/floating-buttons/base/widget-contact-button-base.php:2314
#: modules/link-in-bio/base/widget-link-in-bio-base.php:869
#: modules/nested-accordion/widgets/nested-accordion.php:115
#: modules/nested-accordion/widgets/nested-accordion.php:563
#: modules/nested-tabs/widgets/nested-tabs.php:113
msgid "Title"
msgstr "Titolo"

#: core/common/modules/finder/categories/edit.php:30
#: includes/controls/popover-toggle.php:68 assets/js/ai-admin.js:2328
#: assets/js/ai-admin.js:10186 assets/js/ai-admin.js:10193
#: assets/js/ai-gutenberg.js:2466 assets/js/ai-gutenberg.js:10404
#: assets/js/ai-gutenberg.js:10411 assets/js/ai-media-library.js:2328
#: assets/js/ai-media-library.js:10186 assets/js/ai-media-library.js:10193
#: assets/js/ai-unify-product-images.js:2328
#: assets/js/ai-unify-product-images.js:10186
#: assets/js/ai-unify-product-images.js:10193 assets/js/ai.js:3111
#: assets/js/ai.js:11647 assets/js/ai.js:11654 assets/js/app.js:5022
#: assets/js/element-manager-admin.js:873
#: assets/js/element-manager-admin.js:929
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:54
msgid "Edit"
msgstr "Modifica"

#: includes/widgets/button.php:48 includes/widgets/button.php:115
#: includes/widgets/button.php:126
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:33
#: modules/floating-buttons/base/widget-floating-bars-base.php:567
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1098
msgid "Button"
msgstr "Pulsante"

#: includes/widgets/heading.php:218 includes/widgets/traits/button-trait.php:35
#: modules/floating-buttons/base/widget-contact-button-base.php:1120
#: modules/floating-buttons/base/widget-contact-button-base.php:1545
#: modules/floating-buttons/base/widget-contact-button-base.php:1927
#: modules/floating-buttons/base/widget-contact-button-base.php:2277
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1407
msgid "Small"
msgstr "Piccolo"

#: includes/widgets/heading.php:219 includes/widgets/traits/button-trait.php:36
#: modules/floating-buttons/base/widget-contact-button-base.php:1121
#: modules/floating-buttons/base/widget-contact-button-base.php:1546
#: modules/floating-buttons/base/widget-contact-button-base.php:1928
#: modules/floating-buttons/base/widget-contact-button-base.php:2278
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1408
msgid "Medium"
msgstr "Medio"

#: includes/widgets/heading.php:220 includes/widgets/traits/button-trait.php:37
#: modules/floating-buttons/base/widget-contact-button-base.php:1122
#: modules/floating-buttons/base/widget-contact-button-base.php:1547
#: modules/floating-buttons/base/widget-contact-button-base.php:1929
#: modules/floating-buttons/base/widget-contact-button-base.php:2279
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1409
msgid "Large"
msgstr "Grande"

#: includes/widgets/heading.php:221
msgid "XL"
msgstr "XL"

#: includes/widgets/heading.php:222
msgid "XXL"
msgstr "XXL"

#: includes/widgets/accordion.php:419 includes/widgets/divider.php:470
#: includes/widgets/heading.php:266 includes/widgets/icon-box.php:300
#: includes/widgets/icon-list.php:259 includes/widgets/icon.php:185
#: includes/widgets/image-box.php:268 includes/widgets/image-carousel.php:844
#: includes/widgets/image-gallery.php:367 includes/widgets/image.php:259
#: includes/widgets/image.php:577 includes/widgets/rating.php:199
#: includes/widgets/social-icons.php:324 includes/widgets/star-rating.php:215
#: includes/widgets/tabs.php:205 includes/widgets/tabs.php:235
#: includes/widgets/tabs.php:422 includes/widgets/testimonial.php:242
#: includes/widgets/text-editor.php:258 includes/widgets/toggle.php:443
#: includes/widgets/traits/button-trait.php:284
#: modules/shapes/widgets/text-path.php:172
msgid "Alignment"
msgstr "Allineamento"

#: includes/elements/column.php:747 includes/elements/section.php:1203
#: includes/widgets/heading.php:282 includes/widgets/icon-box.php:316
#: includes/widgets/image-box.php:284 includes/widgets/image-carousel.php:860
#: includes/widgets/image-gallery.php:383 includes/widgets/image.php:593
#: includes/widgets/star-rating.php:231 includes/widgets/text-editor.php:274
msgid "Justified"
msgstr "Giustificato"

#: includes/controls/groups/flex-item.php:125 includes/widgets/alert.php:353
#: includes/widgets/common-base.php:1153 includes/widgets/divider.php:639
#: includes/widgets/divider.php:847 includes/widgets/heading.php:214
#: includes/widgets/icon-box.php:509 includes/widgets/icon-list.php:491
#: includes/widgets/icon.php:306 includes/widgets/image-carousel.php:600
#: includes/widgets/image-carousel.php:684 includes/widgets/rating.php:60
#: includes/widgets/social-icons.php:403 includes/widgets/star-rating.php:326
#: includes/widgets/text-editor.php:474
#: includes/widgets/traits/button-trait.php:114 includes/widgets/video.php:837
#: modules/floating-buttons/base/widget-contact-button-base.php:1116
#: modules/floating-buttons/base/widget-contact-button-base.php:1541
#: modules/floating-buttons/base/widget-contact-button-base.php:1923
#: modules/floating-buttons/base/widget-contact-button-base.php:2273
#: modules/floating-buttons/base/widget-floating-bars-base.php:490
#: modules/floating-buttons/base/widget-floating-bars-base.php:1088
#: modules/floating-buttons/base/widget-floating-bars-base.php:1313
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1403
#: modules/nested-accordion/widgets/nested-accordion.php:603
#: modules/nested-tabs/widgets/nested-tabs.php:895
#: modules/shapes/widgets/text-path.php:251
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:152
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:11
msgid "Size"
msgstr "Dimensione"

#: includes/widgets/accordion.php:185 includes/widgets/accordion.php:408
#: includes/widgets/alert.php:173 includes/widgets/divider.php:509
#: includes/widgets/divider.php:561 includes/widgets/divider.php:821
#: includes/widgets/icon-box.php:117 includes/widgets/icon-box.php:386
#: includes/widgets/icon-list.php:156 includes/widgets/icon-list.php:415
#: includes/widgets/icon.php:44 includes/widgets/icon.php:111
#: includes/widgets/icon.php:118 includes/widgets/icon.php:177
#: includes/widgets/rating.php:52 includes/widgets/rating.php:177
#: includes/widgets/social-icons.php:116 includes/widgets/social-icons.php:353
#: includes/widgets/star-rating.php:168 includes/widgets/toggle.php:188
#: includes/widgets/toggle.php:432 includes/widgets/traits/button-trait.php:126
#: includes/widgets/video.php:698
#: modules/floating-buttons/base/widget-contact-button-base.php:495
#: modules/floating-buttons/base/widget-floating-bars-base.php:113
#: modules/floating-buttons/base/widget-floating-bars-base.php:181
#: modules/floating-buttons/base/widget-floating-bars-base.php:325
#: modules/floating-buttons/base/widget-floating-bars-base.php:399
#: modules/floating-buttons/base/widget-floating-bars-base.php:1267
#: modules/nested-accordion/widgets/nested-accordion.php:205
#: modules/nested-accordion/widgets/nested-accordion.php:595
#: modules/nested-tabs/widgets/nested-tabs.php:126
#: modules/nested-tabs/widgets/nested-tabs.php:847
msgid "Icon"
msgstr "Icona"

#: core/dynamic-tags/tag.php:95 includes/widgets/counter.php:279
#: modules/nested-tabs/widgets/nested-tabs.php:207
#: modules/nested-tabs/widgets/nested-tabs.php:873
msgid "Before"
msgstr "Prima"

#: core/dynamic-tags/tag.php:105 includes/widgets/counter.php:283
#: modules/nested-tabs/widgets/nested-tabs.php:203
#: modules/nested-tabs/widgets/nested-tabs.php:865
msgid "After"
msgstr "Dopo"

#: core/base/traits/shared-widget-controls-trait.php:156
#: includes/elements/column.php:547 includes/elements/container.php:991
#: includes/elements/section.php:831 includes/widgets/common-base.php:966
#: modules/floating-buttons/base/widget-floating-bars-base.php:761
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:15
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:244
msgid "Border"
msgstr "Bordo"

#: includes/controls/groups/background.php:292
#: includes/widgets/common-base.php:1116 includes/widgets/image-box.php:351
#: includes/widgets/image-carousel.php:735 includes/widgets/image.php:45
#: includes/widgets/image.php:126 includes/widgets/image.php:251
#: includes/widgets/testimonial.php:317
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:32
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:66
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:41
#: assets/js/packages/editor-controls/editor-controls.strings.js:143
msgid "Image"
msgstr "Immagine"

#: includes/widgets/audio.php:152 includes/widgets/image-carousel.php:443
#: includes/widgets/video.php:361
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:80
msgid "Autoplay"
msgstr "Autoplay"

#: core/kits/documents/tabs/settings-lightbox.php:56
#: includes/widgets/counter.php:45 includes/widgets/counter.php:127
#: includes/widgets/counter.php:267
msgid "Counter"
msgstr "Contatore"

#: includes/widgets/counter.php:491
#: modules/floating-buttons/base/widget-contact-button-base.php:333
#: modules/floating-buttons/base/widget-contact-button-base.php:887
#: modules/link-in-bio/base/widget-link-in-bio-base.php:483
#: modules/link-in-bio/base/widget-link-in-bio-base.php:731
msgid "Number"
msgstr "Numero"

#: includes/widgets/divider.php:46 includes/widgets/divider.php:378
#: includes/widgets/divider.php:528 includes/widgets/divider.php:578
#: includes/widgets/icon-list.php:282
#: modules/atomic-widgets/elements/atomic-divider/atomic-divider.php:35
msgid "Divider"
msgstr "Divisore"

#: includes/widgets/spacer.php:42 includes/widgets/spacer.php:123
msgid "Spacer"
msgstr "Distanziatore"

#: includes/widgets/divider.php:605 includes/widgets/icon-list.php:318
#: modules/floating-buttons/base/widget-contact-button-base.php:2552
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1218
msgid "Weight"
msgstr "Spessore"

#: core/kits/documents/tabs/theme-style-form-fields.php:78
#: core/kits/documents/tabs/theme-style-typography.php:126
#: core/kits/documents/tabs/theme-style-typography.php:155
#: core/kits/documents/tabs/theme-style-typography.php:200
#: includes/controls/box-shadow.php:104
#: includes/controls/groups/background.php:170
#: includes/controls/text-shadow.php:97 includes/elements/column.php:458
#: includes/elements/container.php:867 includes/elements/container.php:1225
#: includes/elements/section.php:726 includes/elements/section.php:1008
#: includes/widgets/accordion.php:337 includes/widgets/accordion.php:439
#: includes/widgets/accordion.php:507 includes/widgets/alert.php:418
#: includes/widgets/alert.php:435 includes/widgets/divider.php:589
#: includes/widgets/divider.php:733 includes/widgets/heading.php:335
#: includes/widgets/icon-box.php:677 includes/widgets/icon-box.php:701
#: includes/widgets/icon-box.php:764 includes/widgets/icon-list.php:395
#: includes/widgets/icon-list.php:432 includes/widgets/icon-list.php:457
#: includes/widgets/icon-list.php:661 includes/widgets/icon-list.php:685
#: includes/widgets/image-box.php:645 includes/widgets/image-box.php:669
#: includes/widgets/image-box.php:732 includes/widgets/image-carousel.php:620
#: includes/widgets/image-carousel.php:704 includes/widgets/progress.php:306
#: includes/widgets/progress.php:367 includes/widgets/rating.php:110
#: includes/widgets/social-icons.php:208 includes/widgets/social-icons.php:361
#: includes/widgets/star-rating.php:376 includes/widgets/tabs.php:366
#: includes/widgets/tabs.php:459 includes/widgets/toggle.php:370
#: includes/widgets/toggle.php:463 includes/widgets/toggle.php:531
#: includes/widgets/video.php:821
#: modules/floating-buttons/base/widget-contact-button-base.php:1888
#: modules/floating-buttons/base/widget-contact-button-base.php:2287
#: modules/floating-buttons/base/widget-contact-button-base.php:2300
#: modules/floating-buttons/base/widget-contact-button-base.php:2538
#: modules/floating-buttons/base/widget-floating-bars-base.php:422
#: modules/floating-buttons/base/widget-floating-bars-base.php:540
#: modules/floating-buttons/base/widget-floating-bars-base.php:1074
#: modules/floating-buttons/base/widget-floating-bars-base.php:1275
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1207
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1392
#: modules/nested-accordion/widgets/nested-accordion.php:686
#: modules/nested-accordion/widgets/nested-accordion.php:749
#: modules/nested-tabs/widgets/nested-tabs.php:744
#: modules/nested-tabs/widgets/nested-tabs.php:780
#: modules/nested-tabs/widgets/nested-tabs.php:816
#: modules/nested-tabs/widgets/nested-tabs.php:942
#: modules/nested-tabs/widgets/nested-tabs.php:959
#: modules/nested-tabs/widgets/nested-tabs.php:976
#: modules/shapes/widgets/text-path.php:416
#: modules/shapes/widgets/text-path.php:440
#: modules/shapes/widgets/text-path.php:508
#: modules/shapes/widgets/text-path.php:528
#: modules/shapes/widgets/text-path.php:579
#: modules/shapes/widgets/text-path.php:599 assets/js/editor.js:48186
#: assets/js/editor.js:48229
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:72
#: assets/js/packages/editor-controls/editor-controls.strings.js:124
#: assets/js/packages/editor-controls/editor-controls.strings.js:125
#: assets/js/packages/editor-controls/editor-controls.strings.js:145
msgid "Color"
msgstr "Colore"

#: includes/widgets/image-gallery.php:267
msgid "Random"
msgstr "Casuale"

#: core/kits/documents/tabs/theme-style-images.php:21
#: core/kits/documents/tabs/theme-style-images.php:51
#: includes/controls/groups/background.php:623
#: includes/widgets/image-gallery.php:278 assets/js/ai-admin.js:3593
#: assets/js/ai-gutenberg.js:3731 assets/js/ai-media-library.js:3593
#: assets/js/ai-unify-product-images.js:3593 assets/js/ai.js:4409
msgid "Images"
msgstr "Immagini"

#: core/kits/documents/tabs/settings-lightbox.php:101
#: core/kits/documents/tabs/settings-lightbox.php:118
#: includes/widgets/image-carousel.php:410
#: includes/widgets/image-carousel.php:416
#: includes/widgets/image-carousel.php:833
#: includes/widgets/image-gallery.php:170
#: includes/widgets/image-gallery.php:356 includes/widgets/image.php:158
#: includes/widgets/image.php:565
msgid "Caption"
msgstr "Didascalia"

#: includes/widgets/google-maps.php:44 includes/widgets/google-maps.php:125
#: includes/widgets/google-maps.php:212
msgid "Google Maps"
msgstr "Google Maps"

#: includes/widgets/google-maps.php:150
msgid "London Eye, London, United Kingdom"
msgstr "London Eye, London, United Kingdom"

#: includes/widgets/alert.php:136 includes/widgets/heading.php:192
#: includes/widgets/icon-box.php:173 includes/widgets/image-box.php:148
#: includes/widgets/progress.php:122
msgid "Enter your title"
msgstr "Immetti il titolo"

#: core/base/traits/shared-widget-controls-trait.php:23
#: includes/elements/column.php:264 includes/elements/container.php:588
#: includes/elements/section.php:498 includes/widgets/divider.php:538
#: includes/widgets/heading.php:234
#: modules/atomic-widgets/elements/div-block/div-block.php:75
msgid "HTML Tag"
msgstr "Tag HTML"

#: includes/widgets/divider.php:837 includes/widgets/icon-box.php:135
#: includes/widgets/icon.php:136 includes/widgets/text-editor.php:426
msgid "Framed"
msgstr "Incorniciato"

#: includes/widgets/common-base.php:1100 includes/widgets/icon-box.php:148
#: includes/widgets/icon.php:146 includes/widgets/social-icons.php:280
msgid "Shape"
msgstr "Forma"

#: includes/widgets/common-base.php:136 includes/widgets/icon-box.php:153
#: includes/widgets/icon.php:151 includes/widgets/social-icons.php:286
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1678
#: modules/shapes/module.php:45
msgid "Circle"
msgstr "Cerchio"

#: includes/widgets/icon-box.php:151 includes/widgets/icon.php:149
#: includes/widgets/social-icons.php:284
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1679
#: assets/js/ai-admin.js:11356 assets/js/ai-gutenberg.js:11574
#: assets/js/ai-media-library.js:11356
#: assets/js/ai-unify-product-images.js:11356 assets/js/ai.js:12817
msgid "Square"
msgstr "Quadrato"

#: includes/widgets/image-carousel.php:521
msgid "Slide"
msgstr "Slide"

#: includes/widgets/tabs.php:46 includes/widgets/tabs.php:118
#: includes/widgets/tabs.php:267 modules/nested-tabs/widgets/nested-tabs.php:34
#: modules/nested-tabs/widgets/nested-tabs.php:107
#: modules/nested-tabs/widgets/nested-tabs.php:444
msgid "Tabs"
msgstr "Schede"

#: includes/widgets/tabs.php:169
#: modules/nested-tabs/widgets/nested-tabs.php:172
msgid "Tab #1"
msgstr "Scheda #1"

#: includes/widgets/tabs.php:173
#: modules/nested-tabs/widgets/nested-tabs.php:175
msgid "Tab #2"
msgstr "Scheda #2"

#: includes/widgets/toggle.php:46 includes/widgets/toggle.php:123
#: includes/widgets/toggle.php:276
msgid "Toggle"
msgstr "Commutatore"

#: includes/widgets/toggle.php:173
msgid "Toggle #1"
msgstr "Commutatore #1"

#: includes/widgets/toggle.php:177
msgid "Toggle #2"
msgstr "Commutatore #2"

#: core/base/providers/social-network-provider.php:138
#: includes/widgets/video.php:159
#: modules/atomic-widgets/elements/atomic-youtube/atomic-youtube.php:35
msgid "YouTube"
msgstr "YouTube"

#: includes/widgets/video.php:759
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:124
msgid "Aspect Ratio"
msgstr "Proporzioni"

#: includes/managers/elements.php:288
msgid "Basic"
msgstr "Base"

#: includes/elements/container.php:1883 includes/elements/section.php:1390
msgid "Visibility"
msgstr "Visibilità"

#: includes/widgets/sidebar.php:42 includes/widgets/sidebar.php:106
msgid "Sidebar"
msgstr "Barra laterale"

#: includes/widgets/menu-anchor.php:121
msgid "This ID will be the CSS ID you will have to use in your own page, Without #."
msgstr "Questo ID sarà il CSS ID da utilizzare nella tua pagina senza #."

#: core/kits/documents/tabs/settings-layout.php:24
#: includes/elements/column.php:118 includes/elements/container.php:1368
#: includes/elements/section.php:231 includes/managers/controls.php:338
#: includes/managers/elements.php:284 includes/widgets/common-base.php:292
#: includes/widgets/icon-list.php:117
#: modules/floating-buttons/base/widget-contact-button-base.php:2928
#: modules/floating-buttons/base/widget-floating-bars-base.php:1425
#: modules/nested-accordion/widgets/nested-accordion.php:107
#: assets/js/editor.js:37466
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:9
msgid "Layout"
msgstr "Layout"

#: includes/elements/column.php:924 includes/elements/container.php:1875
#: includes/elements/section.php:1366 includes/managers/controls.php:337
#: includes/widgets/common-base.php:1332
#: modules/floating-buttons/base/widget-contact-button-base.php:3061
#: modules/floating-buttons/base/widget-floating-bars-base.php:1467
msgid "Responsive"
msgstr "Responsive"

#: includes/widgets/video.php:513
msgid "Suggested Videos"
msgstr "Video consigliati"

#: includes/widgets/image-carousel.php:456
msgid "Pause on Hover"
msgstr "Pausa al passaggio del mouse"

#: includes/widgets/image-carousel.php:237
#: includes/widgets/image-carousel.php:571
msgid "Arrows"
msgstr "Frecce"

#: includes/widgets/sidebar.php:93 includes/widgets/sidebar.php:113
msgid "Choose Sidebar"
msgstr "Seleziona una barra laterale"

#: includes/widgets/sidebar.php:91
msgid "No sidebars were found"
msgstr "Non è stata trovata alcuna barra laterale"

#: includes/widgets/progress.php:204
msgid "Display Percentage"
msgstr "Visualizza percentuale"

#: includes/widgets/progress.php:45 includes/widgets/progress.php:110
#: includes/widgets/progress.php:236
msgid "Progress Bar"
msgstr "Barra di progressione"

#: includes/widgets/menu-anchor.php:120
msgid "For Example: About"
msgstr "Per esempio: About"

#: includes/widgets/icon-box.php:330
#: includes/widgets/traits/button-trait.php:175
#: modules/floating-buttons/base/widget-contact-button-base.php:1162
#: modules/floating-buttons/base/widget-contact-button-base.php:2418
#: modules/floating-buttons/base/widget-floating-bars-base.php:606
#: modules/floating-buttons/base/widget-floating-bars-base.php:1331
msgid "Icon Spacing"
msgstr "Spaziatura dell'icona"

#: includes/widgets/icon-box.php:45 includes/widgets/icon-box.php:110
msgid "Icon Box"
msgstr "Riquadro icona"

#: includes/widgets/icon-box.php:237
#: includes/widgets/traits/button-trait.php:142
#: modules/floating-buttons/base/widget-contact-button-base.php:1132
#: modules/floating-buttons/base/widget-contact-button-base.php:2398
#: modules/floating-buttons/base/widget-floating-bars-base.php:576
#: modules/floating-buttons/base/widget-floating-bars-base.php:1286
msgid "Icon Position"
msgstr "Posizione dell'icona"

#: core/common/modules/finder/categories/general.php:55
#: modules/system-info/module.php:157
#: modules/system-info/system-info-menu-item.php:29
#: modules/system-info/system-info-menu-item.php:33
msgid "System Info"
msgstr "Informazioni di sistema"

#: includes/elements/section.php:401
msgid "Column Position"
msgstr "Posizione colonna"

#: includes/elements/section.php:337 includes/elements/section.php:377
msgid "Minimum Height"
msgstr "Altezza minima"

#: includes/elements/container.php:484 includes/elements/section.php:327
#: includes/elements/section.php:367
msgid "Min Height"
msgstr "Altezza min"

#: includes/widgets/video.php:635 includes/widgets/video.php:642
#: includes/widgets/video.php:797
msgid "Image Overlay"
msgstr "Immagine sovrapposta"

#: includes/widgets/video.php:409
msgid "Player Controls"
msgstr "Controlli lettore"

#: includes/widgets/toggle.php:147
msgid "Toggle Content"
msgstr "Contenuto commutatore"

#: includes/widgets/toggle.php:134
msgid "Toggle Title"
msgstr "Titolo commutatore"

#: includes/widgets/toggle.php:168
msgid "Toggle Items"
msgstr "Elementi del commutatore"

#: includes/widgets/text-editor.php:46 includes/widgets/text-editor.php:133
#: includes/widgets/text-editor.php:250
msgid "Text Editor"
msgstr "Editor di testo"

#: includes/widgets/tabs.php:142 includes/widgets/tabs.php:143
msgid "Tab Content"
msgstr "Contenuto della scheda"

#: includes/widgets/tabs.php:129 includes/widgets/tabs.php:130
#: modules/nested-tabs/widgets/nested-tabs.php:115
#: modules/nested-tabs/widgets/nested-tabs.php:116
msgid "Tab Title"
msgstr "Titolo della scheda"

#: includes/widgets/tabs.php:164
#: modules/nested-tabs/widgets/nested-tabs.php:167
msgid "Tabs Items"
msgstr "Elementi delle schede"

#: includes/widgets/progress.php:225
msgid "Web Designer"
msgstr "Disegnatore web"

#: includes/widgets/progress.php:219 includes/widgets/progress.php:355
msgid "Inner Text"
msgstr "Testo interno"

#: includes/widgets/image-box.php:212 includes/widgets/testimonial.php:217
msgid "Image Position"
msgstr "Posizione immagine"

#: includes/widgets/image-box.php:298 includes/widgets/image-carousel.php:787
msgid "Image Spacing"
msgstr "Spaziatura immagine"

#: modules/floating-buttons/base/widget-contact-button-base.php:205
#: modules/floating-buttons/base/widget-contact-button-base.php:235
#: modules/floating-buttons/base/widget-contact-button-base.php:1319
#: modules/floating-buttons/base/widget-contact-button-base.php:2019
#: modules/floating-buttons/base/widget-contact-button-base.php:2628
#: modules/floating-buttons/base/widget-contact-button-base.php:2697
#: modules/floating-buttons/base/widget-floating-bars-base.php:1141
msgid "Icon Color"
msgstr "Colore dell'icona"

#: includes/widgets/image-carousel.php:363
#: includes/widgets/image-gallery.php:190 includes/widgets/image.php:197
msgid "Media File"
msgstr "File media"

#: includes/controls/gallery.php:94 includes/widgets/image-carousel.php:162
#: includes/widgets/image-gallery.php:137
msgid "Add Images"
msgstr "Aggiungi immagini"

#: includes/widgets/counter.php:235
msgid "Cool Number"
msgstr "Numero fantastico"

#: includes/widgets/counter.php:134
msgid "Starting Number"
msgstr "Numero iniziale"

#: includes/widgets/image-carousel.php:202
msgid "Slides to Scroll"
msgstr "Slide da scorrere"

#: includes/widgets/image-carousel.php:185
msgid "Slides to Show"
msgstr "Slide da visualizzare"

#: core/kits/documents/tabs/theme-style-buttons.php:152
#: core/kits/documents/tabs/theme-style-buttons.php:227
#: core/kits/documents/tabs/theme-style-form-fields.php:233
#: core/kits/documents/tabs/theme-style-images.php:83
#: core/kits/documents/tabs/theme-style-images.php:154
#: includes/elements/column.php:572 includes/elements/column.php:609
#: includes/elements/container.php:1036 includes/elements/container.php:1087
#: includes/elements/section.php:855 includes/elements/section.php:891
#: includes/widgets/common-base.php:991 includes/widgets/common-base.php:1028
#: includes/widgets/divider.php:1025 includes/widgets/icon-box.php:606
#: includes/widgets/icon.php:407 includes/widgets/image-box.php:459
#: includes/widgets/image-carousel.php:819
#: includes/widgets/image-gallery.php:342 includes/widgets/image.php:540
#: includes/widgets/progress.php:343 includes/widgets/social-icons.php:509
#: includes/widgets/testimonial.php:355 includes/widgets/text-editor.php:529
#: includes/widgets/traits/button-trait.php:489
#: modules/nested-accordion/widgets/nested-accordion.php:461
#: modules/nested-accordion/widgets/nested-accordion.php:526
#: modules/nested-tabs/widgets/nested-tabs.php:688
#: modules/nested-tabs/widgets/nested-tabs.php:1028
msgid "Border Radius"
msgstr "Raggio del bordo"

#: modules/system-info/module.php:160 modules/system-info/module.php:184
msgid "Download System Info"
msgstr "Scarica le informazioni di sistema"

#: modules/system-info/module.php:164
msgid "Copy & Paste Info"
msgstr "Copia e incolla le informazioni"

#: core/role-manager/role-manager.php:74
msgid "Exclude Roles"
msgstr "Escludi i ruoli"

#: includes/elements/section.php:326 includes/elements/section.php:366
msgid "Fit To Screen"
msgstr "Pieno schermo"

#: includes/elements/column.php:161
msgid "Column Width"
msgstr "Larghezza colonna"

#: modules/floating-buttons/documents/floating-buttons.php:31
msgid "Go To Dashboard"
msgstr "Vai alla bacheca"

#: includes/editor-templates/panel.php:71
#: includes/editor-templates/panel.php:72
msgid "Widgets Panel"
msgstr "Pannello dei widget"

#: includes/editor-templates/global.php:34
msgid "Add New Section"
msgstr "Aggiungi una nuova sezione"

#: includes/controls/repeater.php:178
#: modules/floating-buttons/base/widget-contact-button-base.php:1007
#: modules/floating-buttons/base/widget-floating-bars-base.php:367
#: modules/nested-accordion/widgets/nested-accordion.php:161
msgid "Add Item"
msgstr "Aggiungi elemento"

#: includes/controls/icon.php:876 includes/controls/icon.php:877
msgid "Select Icon"
msgstr "Selezione icona"

#: includes/controls/groups/border.php:67 includes/widgets/divider.php:344
#: includes/widgets/icon-list.php:301
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:228
msgid "Dotted"
msgstr "Punteggiato"

#: includes/controls/groups/background.php:433
msgctxt "Background Control"
msgid "Scroll"
msgstr "Scorrevole"

#: core/admin/admin.php:223 core/admin/admin.php:231 core/base/document.php:651
#: modules/admin-bar/module.php:124 modules/gutenberg/module.php:101
#: modules/gutenberg/module.php:112 modules/gutenberg/module.php:134
#: assets/js/e-wc-product-editor.js:2678
msgid "Edit with Elementor"
msgstr "Modifica con Elementor"

#: core/experiments/manager.php:389
#: core/kits/documents/tabs/settings-background.php:78
#: includes/base/widget-base.php:297 includes/controls/animation.php:154
#: includes/controls/font.php:66 includes/controls/groups/background.php:319
#: includes/controls/groups/background.php:432
#: includes/controls/groups/background.php:462
#: includes/controls/groups/background.php:483
#: includes/controls/groups/background.php:688
#: includes/controls/groups/background.php:707
#: includes/controls/groups/border.php:63
#: includes/controls/groups/flex-item.php:23
#: includes/controls/groups/typography.php:151
#: includes/controls/groups/typography.php:164
#: includes/controls/groups/typography.php:177
#: includes/controls/groups/typography.php:189
#: includes/editor-templates/panel.php:243 includes/elements/column.php:187
#: includes/elements/column.php:215 includes/elements/column.php:258
#: includes/elements/container.php:559 includes/elements/container.php:582
#: includes/elements/container.php:1534 includes/elements/section.php:287
#: includes/elements/section.php:325 includes/elements/section.php:365
#: includes/elements/section.php:424 includes/elements/section.php:452
#: includes/elements/section.php:492 includes/settings/settings.php:386
#: includes/widgets/common-base.php:341 includes/widgets/common-base.php:558
#: includes/widgets/divider.php:835 includes/widgets/heading.php:217
#: includes/widgets/icon-box.php:133 includes/widgets/icon-list.php:122
#: includes/widgets/icon.php:134 includes/widgets/image-box.php:405
#: includes/widgets/image-carousel.php:188
#: includes/widgets/image-carousel.php:206
#: includes/widgets/image-carousel.php:397
#: includes/widgets/image-carousel.php:774
#: includes/widgets/image-gallery.php:210
#: includes/widgets/image-gallery.php:266
#: includes/widgets/image-gallery.php:289 includes/widgets/image.php:235
#: includes/widgets/image.php:382 includes/widgets/progress.php:171
#: includes/widgets/text-editor.php:181 includes/widgets/text-editor.php:424
#: includes/widgets/traits/button-trait.php:71
#: modules/element-cache/module.php:123
#: modules/floating-buttons/base/widget-contact-button-base.php:1213
#: modules/floating-buttons/base/widget-contact-button-base.php:1265
#: modules/floating-buttons/base/widget-contact-button-base.php:1352
#: modules/floating-buttons/base/widget-contact-button-base.php:1561
#: modules/floating-buttons/base/widget-contact-button-base.php:1727
#: modules/floating-buttons/base/widget-contact-button-base.php:2291
#: modules/floating-buttons/base/widget-contact-button-base.php:2617
#: modules/floating-buttons/base/widget-contact-button-base.php:2686
#: modules/floating-buttons/base/widget-contact-button-base.php:2817
#: modules/link-in-bio/base/widget-link-in-bio-base.php:179
#: modules/page-templates/module.php:301
#: modules/shapes/widgets/text-path.php:203 assets/js/editor.js:46063
#: assets/js/editor.js:46074
msgid "Default"
msgstr "Predefinito"

#: includes/widgets/social-icons.php:523
msgid "Icon Hover"
msgstr "Icona al passaggio del mouse"

#: includes/widgets/counter.php:146
msgid "Ending Number"
msgstr "Numero finale"

#: includes/elements/section.php:288
msgid "No Gap"
msgstr "Nessuna spaziatura"

#: includes/elements/section.php:283 includes/widgets/text-editor.php:202
msgid "Columns Gap"
msgstr "Spaziatura colonne"

#: core/kits/documents/tabs/settings-layout.php:55
#: includes/elements/container.php:391 includes/elements/section.php:249
#: includes/widgets/video.php:954
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1590
msgid "Content Width"
msgstr "Larghezza contenuto"

#: includes/controls/structure.php:65
msgid "Reset"
msgstr "Reimposta"

#: includes/widgets/icon-list.php:145 includes/widgets/icon-list.php:146
msgid "List Item"
msgstr "Lista Elementi"

#: includes/widgets/icon-list.php:199
msgid "List Item #3"
msgstr "Lista Elementi #3"

#: includes/widgets/icon-list.php:192
msgid "List Item #2"
msgstr "Lista Elementi #2"

#: includes/widgets/icon-list.php:185
msgid "List Item #1"
msgstr "Lista Elementi #1"

#: includes/widgets/icon-list.php:45 includes/widgets/icon-list.php:110
msgid "Icon List"
msgstr "Lista Icone"

#: core/kits/documents/tabs/global-colors.php:92
#: core/kits/documents/tabs/global-typography.php:149
msgid "Accent"
msgstr "In risalto"

#: includes/controls/groups/background.php:457
msgctxt "Background Control"
msgid "Repeat"
msgstr "Ripetizione"

#: includes/controls/media.php:189 includes/widgets/image-box.php:117
#: includes/widgets/image.php:133 includes/widgets/testimonial.php:152
#: includes/widgets/video.php:653
#: modules/link-in-bio/base/widget-link-in-bio-base.php:252
#: modules/link-in-bio/base/widget-link-in-bio-base.php:343
#: modules/link-in-bio/base/widget-link-in-bio-base.php:932
#: modules/link-in-bio/base/widget-link-in-bio-base.php:987
msgid "Choose Image"
msgstr "Scegli l'immagine"

#: includes/widgets/progress.php:224
msgid "e.g. Web Designer"
msgstr "es. Disegnatore web"

#: includes/widgets/divider.php:836 includes/widgets/icon-box.php:134
#: includes/widgets/icon.php:135 includes/widgets/text-editor.php:425
msgid "Stacked"
msgstr "Impilato"

#: includes/widgets/divider.php:699 includes/widgets/icon-list.php:518
#: includes/widgets/image-gallery.php:286 includes/widgets/star-rating.php:291
msgid "Gap"
msgstr "Spaziatura"

#: includes/base/element-base.php:890 includes/base/element-base.php:902
#: includes/widgets/divider.php:981 includes/widgets/icon-box.php:569
#: includes/widgets/icon.php:371 modules/shapes/widgets/text-path.php:283
#: assets/js/packages/editor-controls/editor-controls.js:87
#: assets/js/packages/editor-controls/editor-controls.strings.js:114
#: assets/js/packages/editor-controls/editor-controls.strings.js:119
msgid "Rotate"
msgstr "Ruota"

#: includes/editor-templates/hotkeys.php:128
msgid "Page Settings"
msgstr "Impostazioni Pagina"

#: includes/widgets/image-box.php:45 includes/widgets/image-box.php:110
msgid "Image Box"
msgstr "Riquadro immagine"

#: includes/widgets/counter.php:173
msgid "Number Suffix"
msgstr "Suffisso numero"

#: includes/widgets/counter.php:158
msgid "Number Prefix"
msgstr "Prefisso numero"

#: includes/widgets/alert.php:151
msgid "I am a description. Click the edit button to change this text."
msgstr "Sono una descrizione. Fai clic sul pulsante modifica per modificare questo testo."

#: core/document-types/page-base.php:144
#: core/kits/documents/tabs/theme-style-buttons.php:243
#: core/kits/documents/tabs/theme-style-form-fields.php:159
#: includes/elements/column.php:785 includes/elements/container.php:1388
#: includes/elements/section.php:1246 includes/widgets/accordion.php:394
#: includes/widgets/accordion.php:540 includes/widgets/common-base.php:322
#: includes/widgets/divider.php:870 includes/widgets/icon-box.php:527
#: includes/widgets/icon.php:343 includes/widgets/social-icons.php:422
#: includes/widgets/toggle.php:418 includes/widgets/toggle.php:564
#: includes/widgets/traits/button-trait.php:502
#: modules/floating-buttons/base/widget-contact-button-base.php:1461
#: modules/floating-buttons/base/widget-contact-button-base.php:2186
#: modules/floating-buttons/base/widget-contact-button-base.php:2201
#: modules/floating-buttons/base/widget-contact-button-base.php:2749
#: modules/floating-buttons/base/widget-contact-button-base.php:2889
#: modules/floating-buttons/base/widget-floating-bars-base.php:857
#: modules/floating-buttons/base/widget-floating-bars-base.php:1241
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1179
#: modules/nested-accordion/widgets/nested-accordion.php:474
#: modules/nested-accordion/widgets/nested-accordion.php:538
#: modules/nested-tabs/widgets/nested-tabs.php:701
#: modules/nested-tabs/widgets/nested-tabs.php:1051
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:117
msgid "Padding"
msgstr "Rientro"

#: includes/base/element-base.php:1350 includes/controls/dimensions.php:82
#: includes/elements/column.php:188 includes/elements/container.php:1181
#: includes/elements/container.php:1662 includes/elements/section.php:406
#: includes/elements/section.php:425 includes/elements/section.php:964
#: includes/widgets/common-base.php:681 includes/widgets/counter.php:347
#: includes/widgets/icon-box.php:247 includes/widgets/icon-box.php:269
#: includes/widgets/image-box.php:221 includes/widgets/image-box.php:244
#: includes/widgets/testimonial.php:226 includes/widgets/video.php:979
#: modules/floating-buttons/base/widget-contact-button-base.php:2994
#: modules/floating-buttons/base/widget-floating-bars-base.php:1437
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:21
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:150
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:217
msgid "Top"
msgstr "Sopra"

#: includes/base/element-base.php:1358 includes/controls/dimensions.php:84
#: includes/elements/column.php:190 includes/elements/container.php:1182
#: includes/elements/container.php:1666 includes/elements/section.php:408
#: includes/elements/section.php:427 includes/elements/section.php:965
#: includes/widgets/common-base.php:685 includes/widgets/counter.php:355
#: includes/widgets/icon-box.php:277 includes/widgets/image-box.php:252
#: modules/floating-buttons/base/widget-contact-button-base.php:3002
#: modules/floating-buttons/base/widget-floating-bars-base.php:1441
#: assets/js/packages/editor-controls/editor-controls.js:71
#: assets/js/packages/editor-controls/editor-controls.strings.js:24
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:151
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:220
msgid "Bottom"
msgstr "Sotto"

#: includes/widgets/video.php:974
msgid "Content Position"
msgstr "Posizione Contenuto"

#: includes/elements/container.php:396 includes/elements/section.php:254
#: includes/widgets/common-base.php:342 includes/widgets/icon-list.php:216
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1554
msgid "Full Width"
msgstr "Larghezza piena"

#: core/common/modules/finder/categories/site.php:64
msgid "Themes"
msgstr "Temi"

#: core/kits/views/trash-kit-confirmation.php:30
#: includes/editor-templates/hotkeys.php:63
#: includes/editor-templates/templates.php:168
#: includes/editor-templates/templates.php:339
#: includes/editor-templates/templates.php:372
#: includes/editor-templates/templates.php:429 assets/js/editor.js:8928
#: assets/js/editor.js:8947 assets/js/editor.js:9108 assets/js/editor.js:28255
#: assets/js/editor.js:30735 assets/js/editor.js:48239
#: assets/js/import-export-admin.js:272
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:1127
#: assets/js/kit-library.8357091f2047eb2634d3.bundle.js:1269
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:20
#: assets/js/packages/editor-global-classes/editor-global-classes.strings.js:52
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:21
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:29
#: assets/js/packages/editor-variables/editor-variables.js:2
#: assets/js/packages/editor-variables/editor-variables.strings.js:18
#: assets/js/packages/editor-variables/editor-variables.strings.js:30
#: assets/js/packages/editor-variables/editor-variables.strings.js:67
msgid "Delete"
msgstr "Elimina"

#: core/kits/documents/tabs/settings-lightbox.php:130
#: core/kits/documents/tabs/theme-style-form-fields.php:200
#: includes/controls/groups/background.php:174 includes/widgets/alert.php:218
#: includes/widgets/image.php:622 includes/widgets/progress.php:320
#: includes/widgets/tabs.php:345 includes/widgets/video.php:894
#: modules/floating-buttons/base/widget-contact-button-base.php:1237
#: modules/floating-buttons/base/widget-contact-button-base.php:1289
#: modules/floating-buttons/base/widget-contact-button-base.php:1330
#: modules/floating-buttons/base/widget-contact-button-base.php:1376
#: modules/floating-buttons/base/widget-contact-button-base.php:1692
#: modules/floating-buttons/base/widget-contact-button-base.php:1962
#: modules/floating-buttons/base/widget-contact-button-base.php:1998
#: modules/floating-buttons/base/widget-contact-button-base.php:2031
#: modules/floating-buttons/base/widget-contact-button-base.php:2135
#: modules/floating-buttons/base/widget-contact-button-base.php:2161
#: modules/floating-buttons/base/widget-contact-button-base.php:2370
#: modules/floating-buttons/base/widget-contact-button-base.php:2657
#: modules/floating-buttons/base/widget-contact-button-base.php:2726
#: modules/floating-buttons/base/widget-contact-button-base.php:2813
#: modules/floating-buttons/base/widget-contact-button-base.php:2827
#: modules/floating-buttons/base/widget-floating-bars-base.php:674
#: modules/floating-buttons/base/widget-floating-bars-base.php:708
#: modules/floating-buttons/base/widget-floating-bars-base.php:1110
#: modules/floating-buttons/base/widget-floating-bars-base.php:1155
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1128
#: modules/nested-tabs/widgets/nested-tabs.php:506
#: modules/nested-tabs/widgets/nested-tabs.php:564
#: modules/nested-tabs/widgets/nested-tabs.php:648
#: modules/nested-tabs/widgets/nested-tabs.php:1003 assets/js/ai-admin.js:14034
#: assets/js/ai-admin.js:14752 assets/js/ai-gutenberg.js:14252
#: assets/js/ai-gutenberg.js:14970 assets/js/ai-media-library.js:14034
#: assets/js/ai-media-library.js:14752
#: assets/js/ai-unify-product-images.js:14034
#: assets/js/ai-unify-product-images.js:14752 assets/js/ai.js:15495
#: assets/js/ai.js:16213
msgid "Background Color"
msgstr "Colore di sfondo"

#: includes/widgets/accordion.php:352 includes/widgets/accordion.php:451
#: includes/widgets/image-carousel.php:719 includes/widgets/tabs.php:380
#: includes/widgets/toggle.php:385 includes/widgets/toggle.php:475
msgid "Active Color"
msgstr "Colore attivo"

#: core/base/traits/shared-widget-controls-trait.php:207
#: includes/controls/groups/border.php:90 includes/widgets/accordion.php:303
#: includes/widgets/social-icons.php:562 includes/widgets/tabs.php:334
#: includes/widgets/toggle.php:305 includes/widgets/traits/button-trait.php:430
#: modules/floating-buttons/base/widget-floating-bars-base.php:722
#: modules/floating-buttons/base/widget-floating-bars-base.php:814
#: modules/nested-accordion/widgets/nested-accordion.php:514
#: modules/nested-tabs/widgets/nested-tabs.php:522
#: modules/nested-tabs/widgets/nested-tabs.php:580
#: modules/nested-tabs/widgets/nested-tabs.php:664
#: modules/nested-tabs/widgets/nested-tabs.php:1016
msgid "Border Color"
msgstr "Colore del bordo"

#: includes/elements/column.php:731 includes/elements/section.php:1187
msgid "Text Align"
msgstr "Allineamento testo"

#: includes/elements/column.php:719 includes/elements/section.php:1175
msgid "Link Hover Color"
msgstr "Colore del link al passaggio del mouse"

#: includes/elements/column.php:707 includes/elements/section.php:1163
#: includes/widgets/heading.php:389 includes/widgets/text-editor.php:358
#: includes/widgets/text-editor.php:378
msgid "Link Color"
msgstr "Colore del link"

#: core/kits/documents/tabs/settings-lightbox.php:163
#: core/kits/documents/tabs/theme-style-buttons.php:98
#: core/kits/documents/tabs/theme-style-buttons.php:173
#: core/kits/documents/tabs/theme-style-form-fields.php:176
#: core/kits/documents/tabs/theme-style-typography.php:55
#: includes/elements/column.php:695 includes/elements/section.php:1151
#: includes/widgets/alert.php:270 includes/widgets/alert.php:310
#: includes/widgets/counter.php:499 includes/widgets/counter.php:553
#: includes/widgets/heading.php:366 includes/widgets/image-carousel.php:874
#: includes/widgets/image-gallery.php:400 includes/widgets/image.php:607
#: includes/widgets/progress.php:255 includes/widgets/star-rating.php:258
#: includes/widgets/testimonial.php:280 includes/widgets/testimonial.php:378
#: includes/widgets/testimonial.php:423 includes/widgets/text-editor.php:343
#: includes/widgets/traits/button-trait.php:348
#: includes/widgets/traits/button-trait.php:401
#: modules/floating-buttons/base/widget-contact-button-base.php:1583
#: modules/floating-buttons/base/widget-contact-button-base.php:1615
#: modules/floating-buttons/base/widget-contact-button-base.php:1746
#: modules/floating-buttons/base/widget-contact-button-base.php:1777
#: modules/floating-buttons/base/widget-contact-button-base.php:1808
#: modules/floating-buttons/base/widget-contact-button-base.php:2116
#: modules/floating-buttons/base/widget-contact-button-base.php:2323
#: modules/floating-buttons/base/widget-contact-button-base.php:2351
#: modules/floating-buttons/base/widget-contact-button-base.php:2644
#: modules/floating-buttons/base/widget-contact-button-base.php:2713
#: modules/floating-buttons/base/widget-floating-bars-base.php:663
#: modules/floating-buttons/base/widget-floating-bars-base.php:697
#: modules/floating-buttons/base/widget-floating-bars-base.php:1381
#: modules/floating-buttons/base/widget-floating-bars-base.php:1401
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1108
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1304
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1332
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1360
msgid "Text Color"
msgstr "Colore del testo"

#: includes/elements/container.php:395 includes/elements/section.php:253
msgid "Boxed"
msgstr "Boxed"

#: includes/widgets/image-gallery.php:191
msgid "Attachment Page"
msgstr "Pagina dell’allegato"

#: includes/widgets/video.php:682 includes/widgets/video.php:809
#: modules/floating-buttons/base/widget-floating-bars-base.php:267
msgid "Play Icon"
msgstr "Icona riproduzione"

#: includes/editor-templates/panel-elements.php:75
msgid "Search Widget..."
msgstr "Cerca widget..."

#: core/experiments/manager.php:391 core/experiments/manager.php:681
#: modules/element-cache/module.php:124 assets/js/editor.js:27984
#: assets/js/element-manager-admin.js:597
msgid "Inactive"
msgstr "Inattivo"

#: core/experiments/manager.php:390 core/experiments/manager.php:680
#: modules/element-cache/module.php:125
#: modules/floating-buttons/base/widget-contact-button-base.php:1312
#: modules/nested-accordion/widgets/nested-accordion.php:667
#: modules/nested-accordion/widgets/nested-accordion.php:730
#: modules/nested-tabs/widgets/nested-tabs.php:629
#: modules/nested-tabs/widgets/nested-tabs.php:809
#: modules/nested-tabs/widgets/nested-tabs.php:971 assets/js/editor.js:27986
#: assets/js/element-manager-admin.js:594
msgid "Active"
msgstr "Attiva"

#: core/document-types/post.php:65
msgid "Posts"
msgstr "Articoli"

#: modules/nested-tabs/widgets/nested-tabs.php:230
#: modules/nested-tabs/widgets/nested-tabs.php:272
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:22
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:96
msgid "Justify"
msgstr "Giustifica"

#: core/admin/admin-notices.php:658 includes/controls/gallery.php:123
#: includes/controls/media.php:319 includes/widgets/heading.php:477
msgid "Activate Plugin"
msgstr "Attiva il plugin"

#: modules/system-info/module.php:202
msgid "You do not have permission to download this file."
msgstr "Non hai i permessi per scaricare questo file."

#: core/base/document.php:1972
#: core/common/modules/finder/categories/settings.php:49
msgid "General Settings"
msgstr "Impostazioni generali"

#: modules/floating-buttons/base/widget-contact-button-base.php:1846
msgid "Chat Background Color"
msgstr "Colore sfondo della chat"

#: includes/widgets/image-carousel.php:489
msgid "Autoplay Speed"
msgstr "Velocità dell'autoplay"

#: includes/settings/settings.php:271
msgid "Post Types"
msgstr "Tipi di contenuto"

#: includes/widgets/heading.php:49 includes/widgets/heading.php:177
#: includes/widgets/heading.php:258
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:32
#: modules/link-in-bio/base/widget-link-in-bio-base.php:849
#: modules/link-in-bio/base/widget-link-in-bio-base.php:854
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1295
msgid "Heading"
msgstr "Titolo"

#: includes/elements/column.php:683 includes/elements/section.php:1139
msgid "Heading Color"
msgstr "Colore del titolo"

#: includes/widgets/accordion.php:147
msgid "Accordion Content"
msgstr "Contenuto dell'accordion"

#: includes/widgets/accordion.php:134
msgid "Accordion Title"
msgstr "Titolo dell'accordion"

#: includes/widgets/accordion.php:174
msgid "Accordion #2"
msgstr "Accordion #2"

#: includes/widgets/accordion.php:170
msgid "Accordion #1"
msgstr "Accordion #1"

#: includes/widgets/accordion.php:165
msgid "Accordion Items"
msgstr "Elementi dell'accordion"

#: includes/widgets/accordion.php:46 includes/widgets/accordion.php:123
#: includes/widgets/accordion.php:273
#: modules/nested-accordion/widgets/nested-accordion.php:39
#: modules/nested-accordion/widgets/nested-accordion.php:393
msgid "Accordion"
msgstr "Accordion"

#: includes/widgets/menu-anchor.php:115
msgid "The ID of Menu Anchor."
msgstr "L'ID dell'ancora del menu."

#: includes/widgets/menu-anchor.php:43 includes/widgets/menu-anchor.php:108
msgid "Menu Anchor"
msgstr "Ancora del menu"